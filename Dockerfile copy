# ROS1 Noetic Docker环境，支持GUI应用
FROM osrf/ros:noetic-desktop-full

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV ROS_DISTRO=noetic
ENV DISPLAY=:0

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    wget curl git vim nano htop tree \
    # GUI支持
    x11-apps mesa-utils \
    # 网络工具
    net-tools iputils-ping \
    # 开发工具
    build-essential cmake \
    python3-pip python3-dev \
    # ROS依赖
    python3-catkin-tools \
    python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool \
    # SUPER项目特定依赖
    libglfw3-dev libglew-dev libncurses5-dev libncursesw5-dev \
    libeigen3-dev libdw-dev \
    ros-noetic-mavros* ros-noetic-pcl* ros-noetic-rosfmt \
    # 终端增强
    zsh fish tmux \
    # 其他有用工具
    sudo software-properties-common apt-transport-https ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建Eigen软链接
RUN ln -sf /usr/include/eigen3/Eigen /usr/include/Eigen

# 安装Python包
RUN pip3 install --no-cache-dir \
    numpy pandas matplotlib \
    catkin_pkg rospkg

# 创建工作用户（避免使用root）
RUN useradd -m -s /bin/bash -G sudo rosuser && \
    echo "rosuser:rosuser" | chpasswd && \
    echo "rosuser ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 设置工作目录
WORKDIR /home/<USER>

# 创建catkin工作空间
RUN mkdir -p /home/<USER>/super_ws/src && \
    chown -R rosuser:rosuser /home/<USER>/super_ws

# 切换到普通用户
USER rosuser

# 设置ROS环境
RUN echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc && \
    echo "source ~/super_ws/devel/setup.bash" >> ~/.bashrc && \
    echo "export ROS_HOSTNAME=localhost" >> ~/.bashrc && \
    echo "export ROS_MASTER_URI=http://localhost:11311" >> ~/.bashrc

# 安装oh-my-zsh（提升终端体验）
RUN sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended

# 配置zsh环境
RUN echo "# ROS环境配置" >> ~/.zshrc && \
    echo "source /opt/ros/noetic/setup.bash" >> ~/.zshrc && \
    echo "source ~/super_ws/devel/setup.bash" >> ~/.zshrc && \
    echo "export ROS_HOSTNAME=localhost" >> ~/.zshrc && \
    echo "export ROS_MASTER_URI=http://localhost:11311" >> ~/.zshrc && \
    echo "" >> ~/.zshrc && \
    echo "# 基础别名（非ROS相关）" >> ~/.zshrc && \
    echo "alias ll='ls -la --color=auto'" >> ~/.zshrc && \
    echo "alias la='ls -A --color=auto'" >> ~/.zshrc && \
    echo "alias l='ls -CF --color=auto'" >> ~/.zshrc

# 同时配置bash环境（兼容性）
RUN echo "# ROS环境配置" >> ~/.bashrc && \
    echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc && \
    echo "source ~/super_ws/devel/setup.bash" >> ~/.bashrc && \
    echo "export ROS_HOSTNAME=localhost" >> ~/.bashrc && \
    echo "export ROS_MASTER_URI=http://localhost:11311" >> ~/.bashrc && \
    echo "" >> ~/.bashrc && \
    echo "# 基础别名（非ROS相关）" >> ~/.bashrc && \
    echo "alias ll='ls -la --color=auto'" >> ~/.bashrc && \
    echo "alias la='ls -A --color=auto'" >> ~/.bashrc && \
    echo "alias l='ls -CF --color=auto'" >> ~/.bashrc

# 设置默认shell为zsh
RUN sudo chsh -s /usr/bin/zsh rosuser

# 暴露ROS端口
EXPOSE 11311

# 启动命令
CMD ["/bin/zsh"]
