# ROS Noetic + GUI环境 (基于成功配置)
FROM osrf/ros:noetic-desktop-full

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive
ENV ROS_DISTRO=noetic
ENV DISPLAY=:0

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    wget curl git vim nano htop tree \
    # GUI支持
    x11-apps mesa-utils \
    # 网络工具
    net-tools iputils-ping \
    # 开发工具
    build-essential cmake \
    python3-pip python3-dev \
    # ROS依赖
    python3-catkin-tools \
    python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool \
    # GUI相关
    libglfw3-dev libglew-dev \
    qt5-default libqt5opengl5-dev \
    libgl1-mesa-dev libglu1-mesa-dev freeglut3-dev \
    # 其他有用工具
    sudo software-properties-common apt-transport-https ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 安装Python包
RUN pip3 install --no-cache-dir \
    numpy pandas matplotlib \
    catkin_pkg rospkg

# 创建工作用户（避免使用root）
RUN useradd -m -s /bin/bash -G sudo rosuser && \
    echo "rosuser:rosuser" | chpasswd && \
    echo "rosuser ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 设置工作目录
WORKDIR /home/<USER>

# 切换到普通用户
USER rosuser

# 设置ROS环境
RUN echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc && \
    echo "export ROS_HOSTNAME=localhost" >> ~/.bashrc && \
    echo "export ROS_MASTER_URI=http://localhost:11311" >> ~/.bashrc && \
    echo "export DISPLAY=\${DISPLAY:-:0}" >> ~/.bashrc && \
    echo "export QT_X11_NO_MITSHM=1" >> ~/.bashrc && \
    echo "export XAUTHORITY=/tmp/.docker.xauth" >> ~/.bashrc

# 创建GUI测试脚本 (用户可执行)
RUN echo '#!/bin/bash' > ~/test-gui.sh && \
    echo 'set -e' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "=== GUI环境测试 ==="' >> ~/test-gui.sh && \
    echo 'echo "DISPLAY: $DISPLAY"' >> ~/test-gui.sh && \
    echo 'echo "XAUTHORITY: $XAUTHORITY"' >> ~/test-gui.sh && \
    echo 'echo "QT_X11_NO_MITSHM: $QT_X11_NO_MITSHM"' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "检查X11套接字..."' >> ~/test-gui.sh && \
    echo 'ls -la /tmp/.X11-unix/ || echo "X11套接字不存在"' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "检查X认证文件..."' >> ~/test-gui.sh && \
    echo 'ls -la /tmp/.docker.xauth || echo "X认证文件不存在"' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "测试X11连接..."' >> ~/test-gui.sh && \
    echo 'if command -v xset >/dev/null 2>&1; then' >> ~/test-gui.sh && \
    echo '    if timeout 5 xset q >/dev/null 2>&1; then' >> ~/test-gui.sh && \
    echo '        echo "✓ X11连接正常"' >> ~/test-gui.sh && \
    echo '    else' >> ~/test-gui.sh && \
    echo '        echo "✗ X11连接失败"' >> ~/test-gui.sh && \
    echo '    fi' >> ~/test-gui.sh && \
    echo 'fi' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "测试简单GUI应用..."' >> ~/test-gui.sh && \
    echo 'if command -v xeyes >/dev/null 2>&1; then' >> ~/test-gui.sh && \
    echo '    echo "启动xeyes (3秒后自动关闭)..."' >> ~/test-gui.sh && \
    echo '    timeout 3 xeyes >/dev/null 2>&1 && echo "✓ xeyes测试成功" || echo "✗ xeyes测试失败"' >> ~/test-gui.sh && \
    echo 'fi' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "测试RViz..."' >> ~/test-gui.sh && \
    echo 'echo "是否要测试RViz? (y/N)"' >> ~/test-gui.sh && \
    echo 'read -r response' >> ~/test-gui.sh && \
    echo 'if [[ "$response" =~ ^[Yy]$ ]]; then' >> ~/test-gui.sh && \
    echo '    echo "启动RViz (10秒后自动关闭)..."' >> ~/test-gui.sh && \
    echo '    timeout 10 rviz >/dev/null 2>&1 && echo "✓ RViz启动成功" || echo "✗ RViz启动失败"' >> ~/test-gui.sh && \
    echo 'fi' >> ~/test-gui.sh && \
    echo '' >> ~/test-gui.sh && \
    echo 'echo "GUI测试完成"' >> ~/test-gui.sh && \
    chmod +x ~/test-gui.sh

# 暴露ROS端口
EXPOSE 11311

# 默认命令
CMD ["/bin/bash"]
