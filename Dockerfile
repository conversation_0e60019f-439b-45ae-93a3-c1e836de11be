# ROS Noetic + GUI环境 (基于成功配置)
FROM osrf/ros:noetic-desktop-full

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive
ENV ROS_DISTRO=noetic
ENV DISPLAY=:0

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    wget curl git vim nano htop tree \
    # GUI支持
    x11-apps mesa-utils \
    # 网络工具
    net-tools iputils-ping \
    # 开发工具
    build-essential cmake \
    python3-pip python3-dev \
    # ROS依赖
    python3-catkin-tools \
    python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool \
    # ROS包依赖 (Btraj需要)
    ros-noetic-cv-bridge \
    ros-noetic-image-transport \
    ros-noetic-visualization-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-std-msgs \
    ros-noetic-nav-msgs \
    ros-noetic-tf \
    ros-noetic-message-generation \
    ros-noetic-message-runtime \
    # GUI相关
    libglfw3-dev libglew-dev \
    qt5-default libqt5opengl5-dev \
    libgl1-mesa-dev libglu1-mesa-dev freeglut3-dev \
    # Btraj项目依赖
    libarmadillo-dev \
    libopencv-dev \
    libeigen3-dev \
    libpcl-dev \
    ros-noetic-pcl-conversions \
    ros-noetic-pcl-ros \
    # 其他有用工具
    sudo software-properties-common apt-transport-https ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 安装Python包
RUN pip3 install --no-cache-dir \
    numpy pandas matplotlib \
    catkin_pkg rospkg

# 创建工作用户（避免使用root）
RUN useradd -m -s /bin/bash -G sudo rosuser && \
    echo "rosuser:rosuser" | chpasswd && \
    echo "rosuser ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 设置工作目录
WORKDIR /home/<USER>

# 创建catkin工作空间
RUN mkdir -p /home/<USER>/catkin_ws/src && \
    chown -R rosuser:rosuser /home/<USER>/catkin_ws

# 切换到普通用户
USER rosuser

# 设置ROS环境
RUN echo "source /opt/ros/noetic/setup.bash" >> ~/.bashrc && \
    echo "# 自动检测并source catkin工作空间" >> ~/.bashrc && \
    echo "if [ -f ~/catkin_ws/devel/setup.bash ]; then" >> ~/.bashrc && \
    echo "    source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc && \
    echo "fi" >> ~/.bashrc && \
    echo "export ROS_HOSTNAME=localhost" >> ~/.bashrc && \
    echo "export ROS_MASTER_URI=http://localhost:11311" >> ~/.bashrc && \
    echo "export DISPLAY=\${DISPLAY:-:0}" >> ~/.bashrc && \
    echo "export QT_X11_NO_MITSHM=1" >> ~/.bashrc && \
    echo "export XAUTHORITY=/tmp/.docker.xauth" >> ~/.bashrc && \
    echo "# Mosek环境变量" >> ~/.bashrc && \
    echo "export MOSEKLM_LICENSE_FILE=~/mosek/mosek.lic" >> ~/.bashrc

# 创建Btraj环境测试脚本 (用户可执行)
RUN echo '#!/bin/bash' > ~/test-btraj-env.sh && \
    echo 'set -e' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "=== Btraj环境测试 ==="' >> ~/test-btraj-env.sh && \
    echo 'echo "DISPLAY: $DISPLAY"' >> ~/test-btraj-env.sh && \
    echo 'echo "XAUTHORITY: $XAUTHORITY"' >> ~/test-btraj-env.sh && \
    echo 'echo "MOSEKLM_LICENSE_FILE: $MOSEKLM_LICENSE_FILE"' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "=== 检查项目文件 ==="' >> ~/test-btraj-env.sh && \
    echo 'echo "Btraj源码: $([ -d ~/catkin_ws/src/Btraj ] && echo "存在" || echo "不存在")"' >> ~/test-btraj-env.sh && \
    echo 'echo "plan_utils: $([ -d ~/catkin_ws/src/plan_utils ] && echo "存在" || echo "不存在")"' >> ~/test-btraj-env.sh && \
    echo 'echo "Mosek许可证: $([ -f ~/mosek/mosek.lic ] && echo "存在" || echo "不存在")"' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "=== 检查X11环境 ==="' >> ~/test-btraj-env.sh && \
    echo 'ls -la /tmp/.X11-unix/ || echo "X11套接字不存在"' >> ~/test-btraj-env.sh && \
    echo 'ls -la /tmp/.docker.xauth || echo "X认证文件不存在"' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "=== 测试X11连接 ==="' >> ~/test-btraj-env.sh && \
    echo 'if command -v xset >/dev/null 2>&1; then' >> ~/test-btraj-env.sh && \
    echo '    if timeout 5 xset q >/dev/null 2>&1; then' >> ~/test-btraj-env.sh && \
    echo '        echo "✓ X11连接正常"' >> ~/test-btraj-env.sh && \
    echo '    else' >> ~/test-btraj-env.sh && \
    echo '        echo "✗ X11连接失败"' >> ~/test-btraj-env.sh && \
    echo '    fi' >> ~/test-btraj-env.sh && \
    echo 'fi' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "=== 检查工作空间 ==="' >> ~/test-btraj-env.sh && \
    echo 'if [ -f ~/catkin_ws/devel/setup.bash ]; then' >> ~/test-btraj-env.sh && \
    echo '    echo "✓ 工作空间已构建"' >> ~/test-btraj-env.sh && \
    echo '    source ~/catkin_ws/devel/setup.bash' >> ~/test-btraj-env.sh && \
    echo '    if rospack find bezier_planer >/dev/null 2>&1; then' >> ~/test-btraj-env.sh && \
    echo '        echo "✓ bezier_planer包可用"' >> ~/test-btraj-env.sh && \
    echo '    else' >> ~/test-btraj-env.sh && \
    echo '        echo "✗ bezier_planer包不可用"' >> ~/test-btraj-env.sh && \
    echo '    fi' >> ~/test-btraj-env.sh && \
    echo 'else' >> ~/test-btraj-env.sh && \
    echo '    echo "✗ 工作空间未构建，需要运行 catkin_make"' >> ~/test-btraj-env.sh && \
    echo 'fi' >> ~/test-btraj-env.sh && \
    echo '' >> ~/test-btraj-env.sh && \
    echo 'echo "测试完成"' >> ~/test-btraj-env.sh && \
    chmod +x ~/test-btraj-env.sh

# 创建Btraj构建脚本
RUN echo '#!/bin/bash' > ~/build-btraj.sh && \
    echo 'set -e' >> ~/build-btraj.sh && \
    echo '' >> ~/build-btraj.sh && \
    echo 'echo "=== 构建Btraj工作空间 ==="' >> ~/build-btraj.sh && \
    echo 'cd ~/catkin_ws' >> ~/build-btraj.sh && \
    echo '' >> ~/build-btraj.sh && \
    echo 'echo "检查源码..."' >> ~/build-btraj.sh && \
    echo 'if [ ! -d "src/Btraj" ]; then' >> ~/build-btraj.sh && \
    echo '    echo "错误: Btraj源码不存在"' >> ~/build-btraj.sh && \
    echo '    exit 1' >> ~/build-btraj.sh && \
    echo 'fi' >> ~/build-btraj.sh && \
    echo '' >> ~/build-btraj.sh && \
    echo 'echo "使用rosdep安装依赖..."' >> ~/build-btraj.sh && \
    echo 'rosdep update || true' >> ~/build-btraj.sh && \
    echo 'rosdep install --from-paths src --ignore-src -r -y || true' >> ~/build-btraj.sh && \
    echo '' >> ~/build-btraj.sh && \
    echo 'echo "构建工作空间..."' >> ~/build-btraj.sh && \
    echo 'catkin_make -j$(nproc)' >> ~/build-btraj.sh && \
    echo '' >> ~/build-btraj.sh && \
    echo 'echo "构建完成！"' >> ~/build-btraj.sh && \
    echo 'echo "运行以下命令加载环境:"' >> ~/build-btraj.sh && \
    echo 'echo "  source ~/catkin_ws/devel/setup.bash"' >> ~/build-btraj.sh && \
    chmod +x ~/build-btraj.sh

# 创建Btraj启动脚本
RUN echo '#!/bin/bash' > ~/start-btraj.sh && \
    echo 'set -e' >> ~/start-btraj.sh && \
    echo '' >> ~/start-btraj.sh && \
    echo 'echo "=== 启动Btraj仿真 ==="' >> ~/start-btraj.sh && \
    echo '' >> ~/start-btraj.sh && \
    echo '# 确保环境已加载' >> ~/start-btraj.sh && \
    echo 'source ~/.bashrc' >> ~/start-btraj.sh && \
    echo '' >> ~/start-btraj.sh && \
    echo 'if [ ! -f ~/catkin_ws/devel/setup.bash ]; then' >> ~/start-btraj.sh && \
    echo '    echo "错误: 工作空间未构建，请先运行 ./build-btraj.sh"' >> ~/start-btraj.sh && \
    echo '    exit 1' >> ~/start-btraj.sh && \
    echo 'fi' >> ~/start-btraj.sh && \
    echo '' >> ~/start-btraj.sh && \
    echo 'source ~/catkin_ws/devel/setup.bash' >> ~/start-btraj.sh && \
    echo '' >> ~/start-btraj.sh && \
    echo 'echo "启动Btraj仿真..."' >> ~/start-btraj.sh && \
    echo 'echo "RViz将在NoMachine桌面显示"' >> ~/start-btraj.sh && \
    echo 'roslaunch bezier_planer simulation.launch' >> ~/start-btraj.sh && \
    chmod +x ~/start-btraj.sh

# 暴露ROS端口
EXPOSE 11311

# 默认命令
CMD ["/bin/bash"]
