# 简单的ROS Noetic + GUI环境
FROM osrf/ros:noetic-desktop-full

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装GUI相关依赖和测试工具
RUN apt-get update && apt-get install -y \
    # X11和GUI基础包
    xauth x11-apps mesa-utils \
    libxcb1 libxcb-render0 libxcb-shape0 libxcb-xfixes0 \
    libxrandr2 libxinerama1 libxcursor1 libxi6 libgl1-mesa-glx \
    # Qt5支持 (RViz需要)
    qt5-default libqt5opengl5-dev \
    # OpenGL支持
    libgl1-mesa-dev libglu1-mesa-dev freeglut3-dev \
    # 实用工具
    vim curl wget htop tree \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV DISPLAY=:0
ENV QT_X11_NO_MITSHM=1
ENV LIBGL_ALWAYS_INDIRECT=1
ENV XDG_RUNTIME_DIR=/tmp/runtime-ros

# 创建工作目录
WORKDIR /workspace

# 创建GUI测试脚本
RUN echo '#!/bin/bash' > /usr/local/bin/test-gui.sh && \
    echo 'set -e' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "=== GUI环境测试 ==="' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "DISPLAY: $DISPLAY"' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "QT_X11_NO_MITSHM: $QT_X11_NO_MITSHM"' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "LIBGL_ALWAYS_INDIRECT: $LIBGL_ALWAYS_INDIRECT"' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "检查X11套接字..."' >> /usr/local/bin/test-gui.sh && \
    echo 'ls -la /tmp/.X11-unix/ || echo "X11套接字不存在"' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "测试X11连接..."' >> /usr/local/bin/test-gui.sh && \
    echo 'if command -v xset >/dev/null 2>&1; then' >> /usr/local/bin/test-gui.sh && \
    echo '    if timeout 5 xset q >/dev/null 2>&1; then' >> /usr/local/bin/test-gui.sh && \
    echo '        echo "✓ X11连接正常"' >> /usr/local/bin/test-gui.sh && \
    echo '    else' >> /usr/local/bin/test-gui.sh && \
    echo '        echo "✗ X11连接失败"' >> /usr/local/bin/test-gui.sh && \
    echo '    fi' >> /usr/local/bin/test-gui.sh && \
    echo 'fi' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "测试简单GUI应用..."' >> /usr/local/bin/test-gui.sh && \
    echo 'if command -v xeyes >/dev/null 2>&1; then' >> /usr/local/bin/test-gui.sh && \
    echo '    echo "启动xeyes (3秒后自动关闭)..."' >> /usr/local/bin/test-gui.sh && \
    echo '    timeout 3 xeyes >/dev/null 2>&1 && echo "✓ xeyes测试成功" || echo "✗ xeyes测试失败"' >> /usr/local/bin/test-gui.sh && \
    echo 'fi' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "测试RViz..."' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "是否要测试RViz? (y/N)"' >> /usr/local/bin/test-gui.sh && \
    echo 'read -r response' >> /usr/local/bin/test-gui.sh && \
    echo 'if [[ "$response" =~ ^[Yy]$ ]]; then' >> /usr/local/bin/test-gui.sh && \
    echo '    echo "启动RViz (10秒后自动关闭)..."' >> /usr/local/bin/test-gui.sh && \
    echo '    timeout 10 rviz >/dev/null 2>&1 && echo "✓ RViz启动成功" || echo "✗ RViz启动失败"' >> /usr/local/bin/test-gui.sh && \
    echo 'fi' >> /usr/local/bin/test-gui.sh && \
    echo '' >> /usr/local/bin/test-gui.sh && \
    echo 'echo "GUI测试完成"' >> /usr/local/bin/test-gui.sh && \
    chmod +x /usr/local/bin/test-gui.sh

# 配置bash环境
RUN echo '' >> /etc/bash.bashrc && \
    echo '# ROS环境自动加载' >> /etc/bash.bashrc && \
    echo 'source /opt/ros/noetic/setup.bash' >> /etc/bash.bashrc && \
    echo '' >> /etc/bash.bashrc && \
    echo '# X11环境变量' >> /etc/bash.bashrc && \
    echo 'export DISPLAY=${DISPLAY:-:0}' >> /etc/bash.bashrc && \
    echo 'export QT_X11_NO_MITSHM=1' >> /etc/bash.bashrc && \
    echo 'export LIBGL_ALWAYS_INDIRECT=1' >> /etc/bash.bashrc && \
    echo 'export XDG_RUNTIME_DIR=/tmp/runtime-ros' >> /etc/bash.bashrc && \
    echo 'mkdir -p $XDG_RUNTIME_DIR && chmod 700 $XDG_RUNTIME_DIR' >> /etc/bash.bashrc && \
    echo '' >> /etc/bash.bashrc && \
    echo '# 彩色提示符' >> /etc/bash.bashrc && \
    echo 'export PS1="\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ "' >> /etc/bash.bashrc

# 创建运行时目录
RUN mkdir -p /tmp/runtime-ros && chmod 700 /tmp/runtime-ros

# 默认命令
CMD ["bash"]
