# ROS Noetic GUI 测试环境 (Docker Compose)

这是一个简化的Docker Compose配置，专门用于测试ROS Noetic的GUI功能，特别是在Windows SSH -> Linux -> Docker -> NoMachine的环境中。

## 🎯 目标

- 验证ROS Noetic GUI (RViz) 能否在NoMachine桌面正常显示
- 提供一个干净的测试环境，不包含任何项目代码
- 为后续的Btraj项目提供可靠的GUI基础

## 🚀 快速开始

### 1. 启动环境

```bash
# 使用启动脚本 (推荐)
./start-ros-gui.sh
# 选择选项1: 构建并启动容器

# 或直接使用docker-compose
docker-compose up --build -d
```

### 2. 进入容器

```bash
# 使用启动脚本
./start-ros-gui.sh
# 选择选项6: 进入容器shell

# 或直接使用docker-compose
docker-compose exec ros-noetic bash
```

### 3. 测试GUI环境

```bash
# 在容器内运行
test-gui.sh
```

### 4. 测试RViz

```bash
# 在容器内运行
roscore &          # 启动ROS Master
rviz               # 启动RViz (应该在NoMachine桌面显示)
```

## 📁 文件结构

```
.
├── docker-compose.yml     # Docker Compose配置
├── Dockerfile            # 简单的ROS Noetic镜像
├── start-ros-gui.sh      # 启动管理脚本
├── README-compose.md     # 本文件
└── .devcontainer_old/   # 旧的devcontainer配置
```

## 🔧 配置特点

### Docker Compose优势
- **简单配置**: 单个YAML文件定义所有设置
- **易于管理**: 启动/停止/重建都很简单
- **网络透明**: 使用host网络，ROS通信无障碍
- **卷挂载清晰**: X11相关挂载一目了然

### X11配置
- **DISPLAY环境变量**: 自动传递宿主机DISPLAY
- **X11套接字挂载**: `/tmp/.X11-unix:/tmp/.X11-unix:rw`
- **认证文件挂载**: `$HOME/.Xauthority:/root/.Xauthority:rw`
- **Qt兼容性**: `QT_X11_NO_MITSHM=1`
- **OpenGL设置**: `LIBGL_ALWAYS_INDIRECT=1`

## 🛠️ 故障排除

### GUI无法显示
1. **检查NoMachine连接**:
   ```bash
   # 在Linux主机上检查
   echo $DISPLAY
   ls -la /tmp/.X11-unix/
   ```

2. **运行容器内诊断**:
   ```bash
   test-gui.sh
   ```

3. **检查X11挂载**:
   ```bash
   # 在容器内检查
   ls -la /tmp/.X11-unix/
   echo $DISPLAY
   ```

### 容器管理
```bash
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs ros-noetic

# 重新构建
docker-compose build --no-cache

# 完全清理
docker-compose down
docker system prune -f
```

## 📝 测试步骤

### 基础测试
1. 启动容器: `./start-ros-gui.sh` (选项1)
2. 进入容器: `./start-ros-gui.sh` (选项6)
3. 运行GUI测试: `test-gui.sh`
4. 测试xeyes: 应该在NoMachine桌面显示

### ROS GUI测试
1. 启动ROS Master: `roscore &`
2. 启动RViz: `rviz`
3. 验证RViz界面在NoMachine桌面正常显示
4. 测试基本RViz功能 (添加显示项等)

### 成功标准
- ✅ xeyes能在NoMachine桌面显示
- ✅ RViz能正常启动并显示界面
- ✅ RViz界面响应正常，无错误信息
- ✅ 可以在RViz中添加和配置显示项

## 🔄 下一步

如果GUI测试成功，可以:
1. 在此基础上添加Btraj项目挂载
2. 扩展Dockerfile添加Btraj依赖
3. 创建专门的Btraj服务配置

如果GUI测试失败，需要:
1. 检查NoMachine配置
2. 验证Linux主机X11设置
3. 调试Docker X11挂载问题
