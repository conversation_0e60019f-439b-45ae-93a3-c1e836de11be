<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_geometry/geometric_utils.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_0156596343f07f423e58f27e9acfceb9.html">decomp_geometry</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">geometric_utils.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>basic geometry utils  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &lt;iostream&gt;</code><br />
<code>#include &lt;<a class="el" href="data__utils_8h_source.html">decomp_basis/data_utils.h</a>&gt;</code><br />
<code>#include &lt;Eigen/Eigenvalues&gt;</code><br />
</div>
<p><a href="geometric__utils_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a0ce1d8245227b22ff06fe09915db19c5"><td class="memTemplParams" colspan="2"><a class="anchor" id="a0ce1d8245227b22ff06fe09915db19c5"></a>
template&lt;int Dim&gt; </td></tr>
<tr class="memitem:a0ce1d8245227b22ff06fe09915db19c5"><td class="memTemplItemLeft" align="right" valign="top"><a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#a0ce1d8245227b22ff06fe09915db19c5">eigen_value</a> (const <a class="el" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a>&lt; Dim, Dim &gt; &amp;A)</td></tr>
<tr class="memdesc:a0ce1d8245227b22ff06fe09915db19c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate eigen values. <br /></td></tr>
<tr class="separator:a0ce1d8245227b22ff06fe09915db19c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa91b5d566f48c28dff0d9aa08b4abdc2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa91b5d566f48c28dff0d9aa08b4abdc2"></a>
<a class="el" href="data__type_8h.html#a5503e9ed3faaa114b9611829fb322981">Mat2f</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">vec2_to_rotation</a> (const <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &amp;v)</td></tr>
<tr class="memdesc:aa91b5d566f48c28dff0d9aa08b4abdc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate rotation matrix from a vector (aligned with x-axis) <br /></td></tr>
<tr class="separator:aa91b5d566f48c28dff0d9aa08b4abdc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43b12f2a3c67f313976e386933e8f1d3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a43b12f2a3c67f313976e386933e8f1d3"></a>
<a class="el" href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">Mat3f</a>&#160;</td><td class="memItemRight" valign="bottom"><b>vec3_to_rotation</b> (const <a class="el" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> &amp;v)</td></tr>
<tr class="separator:a43b12f2a3c67f313976e386933e8f1d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af844038aa7029551d36f807327590005"><td class="memItemLeft" align="right" valign="top"><a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">sort_pts</a> (const <a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> &amp;pts)</td></tr>
<tr class="memdesc:af844038aa7029551d36f807327590005"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sort points on the same plane in the counter-clockwise order.  <a href="#af844038aa7029551d36f807327590005">More...</a><br /></td></tr>
<tr class="separator:af844038aa7029551d36f807327590005"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79027aaed3d36e00b00eb58e55d95f16"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a79027aaed3d36e00b00eb58e55d95f16"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">line_intersect</a> (const std::pair&lt; <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>, <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &gt; &amp;v1, const std::pair&lt; <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>, <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &gt; &amp;v2, <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &amp;pi)</td></tr>
<tr class="memdesc:a79027aaed3d36e00b00eb58e55d95f16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find intersection between two lines on the same plane, return false if they are not intersected. <br /></td></tr>
<tr class="separator:a79027aaed3d36e00b00eb58e55d95f16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34493497e7d12d4a99e6b9b7cef007f8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a34493497e7d12d4a99e6b9b7cef007f8"></a>
<a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">line_intersects</a> (const <a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; std::pair&lt; <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>, <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &gt;&gt; &amp;lines)</td></tr>
<tr class="memdesc:a34493497e7d12d4a99e6b9b7cef007f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find intersection between multiple lines. <br /></td></tr>
<tr class="separator:a34493497e7d12d4a99e6b9b7cef007f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a36836283fdf3f458b415259fbe606f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a0a36836283fdf3f458b415259fbe606f"></a>
<a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a>&#160;</td><td class="memItemRight" valign="bottom"><b>cal_vertices</b> (const <a class="el" href="structPolyhedron.html">Polyhedron2D</a> &amp;poly)</td></tr>
<tr class="separator:a0a36836283fdf3f458b415259fbe606f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b4b27d3fcc8d4035d9b61a26215fd59"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a8b4b27d3fcc8d4035d9b61a26215fd59"></a>
<a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="data__type_8h.html#a62c46ed3e3ab6773b30439f9be38290b">vec_Vec3f</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">cal_vertices</a> (const <a class="el" href="structPolyhedron.html">Polyhedron3D</a> &amp;poly)</td></tr>
<tr class="memdesc:a8b4b27d3fcc8d4035d9b61a26215fd59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find extreme points of Polyhedron3D. <br /></td></tr>
<tr class="separator:a8b4b27d3fcc8d4035d9b61a26215fd59"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>basic geometry utils </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="af844038aa7029551d36f807327590005"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> sort_pts </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> &amp;&#160;</td>
          <td class="paramname"><em>pts</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Sort points on the same plane in the counter-clockwise order. </p>
<p>if empty, dont sort</p>
<p>calculate center point</p>
<p>sort in body frame </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
