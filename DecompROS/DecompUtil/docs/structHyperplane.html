<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Hyperplane&lt; Dim &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structHyperplane-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Hyperplane&lt; Dim &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="structHyperplane.html" title="Hyperplane class. ">Hyperplane</a> class.  
 <a href="structHyperplane.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:af9fb7b09f18d9199060433dc710f554e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="af9fb7b09f18d9199060433dc710f554e"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Hyperplane</b> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;p, const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;n)</td></tr>
<tr class="separator:af9fb7b09f18d9199060433dc710f554e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa25c61b6fd4a9cdf8987324897f43939"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa25c61b6fd4a9cdf8987324897f43939"></a>
<a class="el" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">signed_dist</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt) const </td></tr>
<tr class="memdesc:aa25c61b6fd4a9cdf8987324897f43939"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the signed distance from point. <br /></td></tr>
<tr class="separator:aa25c61b6fd4a9cdf8987324897f43939"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add0b2123e1bb1923e25cddfb7dd62804"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="add0b2123e1bb1923e25cddfb7dd62804"></a>
<a class="el" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structHyperplane.html#add0b2123e1bb1923e25cddfb7dd62804">dist</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt) const </td></tr>
<tr class="memdesc:add0b2123e1bb1923e25cddfb7dd62804"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the distance from point. <br /></td></tr>
<tr class="separator:add0b2123e1bb1923e25cddfb7dd62804"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:afec3414bc825315a9fd3af4001ee5933"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="afec3414bc825315a9fd3af4001ee5933"></a>
<a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">p_</a></td></tr>
<tr class="memdesc:afec3414bc825315a9fd3af4001ee5933"><td class="mdescLeft">&#160;</td><td class="mdescRight">Point on the plane. <br /></td></tr>
<tr class="separator:afec3414bc825315a9fd3af4001ee5933"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6d2871b9f869a49794b72ff433247030"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a6d2871b9f869a49794b72ff433247030"></a>
<a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">n_</a></td></tr>
<tr class="memdesc:a6d2871b9f869a49794b72ff433247030"><td class="mdescLeft">&#160;</td><td class="mdescRight">Normal of the plane, directional. <br /></td></tr>
<tr class="separator:a6d2871b9f869a49794b72ff433247030"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int Dim&gt;<br />
struct Hyperplane&lt; Dim &gt;</h3>

<p><a class="el" href="structHyperplane.html" title="Hyperplane class. ">Hyperplane</a> class. </p>
</div><hr/>The documentation for this struct was generated from the following file:<ul>
<li>include/decomp_geometry/<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
