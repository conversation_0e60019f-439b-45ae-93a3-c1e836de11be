<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index_a"><span>a</span></a></li>
      <li><a href="#index_c"><span>c</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_f"><span>f</span></a></li>
      <li><a href="#index_g"><span>g</span></a></li>
      <li><a href="#index_h"><span>h</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_l"><span>l</span></a></li>
      <li><a href="#index_p"><span>p</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li class="current"><a href="#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a class="anchor" id="index_a"></a>- a -</h3><ul>
<li>add()
: <a class="el" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">Polyhedron&lt; Dim &gt;</a>
</li>
<li>add_local_bbox()
: <a class="el" href="classLineSegment.html#ac572aa41bea2d3c706d3dd6a1bb24e57">LineSegment&lt; Dim &gt;</a>
, <a class="el" href="classSeedDecomp.html#a0de774b2c94a41f84a6e73a8eefd8e8b">SeedDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>C()
: <a class="el" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">Ellipsoid&lt; Dim &gt;</a>
</li>
<li>cal_closest_dist()
: <a class="el" href="classIterativeDecomp.html#a6df4c75ba768e0b0866ab0de79955639">IterativeDecomp&lt; Dim &gt;</a>
</li>
<li>cal_normals()
: <a class="el" href="structPolyhedron.html#aaf89b0bd80d59b3ff4c7f40b3872e2f3">Polyhedron&lt; Dim &gt;</a>
</li>
<li>closest_hyperplane()
: <a class="el" href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">Ellipsoid&lt; Dim &gt;</a>
</li>
<li>closest_point()
: <a class="el" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">Ellipsoid&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>d()
: <a class="el" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">Ellipsoid&lt; Dim &gt;</a>
</li>
<li>DecompBase()
: <a class="el" href="classDecompBase.html#a4fdc315275a421b40a84aeffacab4a5a">DecompBase&lt; Dim &gt;</a>
</li>
<li>dilate()
: <a class="el" href="classDecompBase.html#a8075ef9c927b23309ca1a3d4144250e5">DecompBase&lt; Dim &gt;</a>
, <a class="el" href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">EllipsoidDecomp&lt; Dim &gt;</a>
, <a class="el" href="classLineSegment.html#a3479e2467a33468749971e12aa15c46f">LineSegment&lt; Dim &gt;</a>
, <a class="el" href="classSeedDecomp.html#a94db31b5d02e4ca19f32c3a5bacc25a2">SeedDecomp&lt; Dim &gt;</a>
</li>
<li>dilate_iter()
: <a class="el" href="classIterativeDecomp.html#ad735b75d676b2f4ee3b8f2015f95559c">IterativeDecomp&lt; Dim &gt;</a>
</li>
<li>dist()
: <a class="el" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">Ellipsoid&lt; Dim &gt;</a>
, <a class="el" href="structHyperplane.html#add0b2123e1bb1923e25cddfb7dd62804">Hyperplane&lt; Dim &gt;</a>
</li>
<li>downsample()
: <a class="el" href="classIterativeDecomp.html#a359ab6e36450d30ea16a6ad885f27648">IterativeDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>EllipsoidDecomp()
: <a class="el" href="classEllipsoidDecomp.html#ac6e757930647fb499d1f1ffeef01b6a4">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_f"></a>- f -</h3><ul>
<li>find_ellipsoid()
: <a class="el" href="classLineSegment.html#a0f2e7b18d162be0481f4e5be1cea5363">LineSegment&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_g"></a>- g -</h3><ul>
<li>get_constraints()
: <a class="el" href="classEllipsoidDecomp.html#aa43d19fea353dfd9466f729c0c38c581">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>get_ellipsoid()
: <a class="el" href="classDecompBase.html#afd3d14a14667f86456f60384df9d81fb">DecompBase&lt; Dim &gt;</a>
</li>
<li>get_ellipsoids()
: <a class="el" href="classEllipsoidDecomp.html#a3c8bf3ede2d7be93d74239cae76495e6">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>get_line_segment()
: <a class="el" href="classLineSegment.html#ac2de795a258c1ee25544188033ecc269">LineSegment&lt; Dim &gt;</a>
</li>
<li>get_obs()
: <a class="el" href="classDecompBase.html#a617f05e12b3f8dbc3b67012f5edbc577">DecompBase&lt; Dim &gt;</a>
</li>
<li>get_path()
: <a class="el" href="classEllipsoidDecomp.html#ad880fd3bbaa9b5ccefcbc4317bbc68a5">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>get_polyhedron()
: <a class="el" href="classDecompBase.html#a2828493d375ef02f3711942f66f399d4">DecompBase&lt; Dim &gt;</a>
</li>
<li>get_polyhedrons()
: <a class="el" href="classEllipsoidDecomp.html#a111a2f74f8cc807d15e70e121bf03f7c">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>get_seed()
: <a class="el" href="classSeedDecomp.html#a06743828a81c150c7368587ee7924e98">SeedDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_h"></a>- h -</h3><ul>
<li>hyperplanes()
: <a class="el" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">Polyhedron&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_i"></a>- i -</h3><ul>
<li>inside()
: <a class="el" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">Ellipsoid&lt; Dim &gt;</a>
, <a class="el" href="structLinearConstraint.html#a23779a58e2a1094971cdcceeac3f7f74">LinearConstraint&lt; Dim &gt;</a>
, <a class="el" href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">Polyhedron&lt; Dim &gt;</a>
</li>
<li>IterativeDecomp()
: <a class="el" href="classIterativeDecomp.html#a7438eb4550b447e1e5422bb99359a39d">IterativeDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_l"></a>- l -</h3><ul>
<li>LinearConstraint()
: <a class="el" href="structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9">LinearConstraint&lt; Dim &gt;</a>
</li>
<li>LineSegment()
: <a class="el" href="classLineSegment.html#a88f0a13c753958dcfc0040d412dcd92a">LineSegment&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_p"></a>- p -</h3><ul>
<li>points_inside()
: <a class="el" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">Ellipsoid&lt; Dim &gt;</a>
, <a class="el" href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">Polyhedron&lt; Dim &gt;</a>
</li>
<li>Polyhedron()
: <a class="el" href="structPolyhedron.html#a8b9a5d2d44059c016486be3e3e05ddeb">Polyhedron&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>sample()
: <a class="el" href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">Ellipsoid&lt; Dim &gt;</a>
</li>
<li>SeedDecomp()
: <a class="el" href="classSeedDecomp.html#ac4cb38597f35ff4b1e27a225436d54dd">SeedDecomp&lt; Dim &gt;</a>
</li>
<li>set_local_bbox()
: <a class="el" href="classDecompBase.html#a43a9d473dfbcdcc3a45d21917186663b">DecompBase&lt; Dim &gt;</a>
, <a class="el" href="classEllipsoidDecomp.html#a3a98cbbe53c7d641b12bc9bb5916d8d6">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>set_obs()
: <a class="el" href="classDecompBase.html#a53cb01307a94dc941f40f1234b676a3b">DecompBase&lt; Dim &gt;</a>
, <a class="el" href="classEllipsoidDecomp.html#aa3ea99617acd7f626ac7e731be9bea9f">EllipsoidDecomp&lt; Dim &gt;</a>
</li>
<li>shrink()
: <a class="el" href="classDecompBase.html#a683f4c7d5b2cc03ac736f224e9cbca2c">DecompBase&lt; Dim &gt;</a>
</li>
<li>signed_dist()
: <a class="el" href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">Hyperplane&lt; Dim &gt;</a>
</li>
<li>simplify()
: <a class="el" href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">IterativeDecomp&lt; Dim &gt;</a>
</li>
</ul>


<h3><a class="anchor" id="index_v"></a>- v -</h3><ul>
<li>volume()
: <a class="el" href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">Ellipsoid&lt; Dim &gt;</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
