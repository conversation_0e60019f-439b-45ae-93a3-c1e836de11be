<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_util/ellipsoid_decomp.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_75993fee8576b97e3d9a8476c1772d17.html">decomp_util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">ellipsoid_decomp.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="ellipsoid__decomp_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#ifndef ELLIPSOID_DECOMP_H</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define ELLIPSOID_DECOMP_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;memory&gt;</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="line__segment_8h.html">decomp_util/line_segment.h</a>&gt;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00017"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html">   17</a></span>&#160;<span class="keyword">class </span><a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp</a> {</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00020"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#ac6e757930647fb499d1f1ffeef01b6a4">   20</a></span>&#160; <a class="code" href="classEllipsoidDecomp.html#ac6e757930647fb499d1f1ffeef01b6a4">EllipsoidDecomp</a>() {}</div><div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#a4c9617e1e2d94ead1ec921e9af7951d0">   26</a></span>&#160; <a class="code" href="classEllipsoidDecomp.html#a4c9617e1e2d94ead1ec921e9af7951d0">EllipsoidDecomp</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;origin, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;dim) {</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;   global_bbox_min_ = origin;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;   global_bbox_max_ = origin + dim;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; }</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00032"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#aa3ea99617acd7f626ac7e731be9bea9f">   32</a></span>&#160; <span class="keywordtype">void</span> <a class="code" href="classEllipsoidDecomp.html#aa3ea99617acd7f626ac7e731be9bea9f">set_obs</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;obs) { obs_ = obs; }</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#a3a98cbbe53c7d641b12bc9bb5916d8d6">   35</a></span>&#160; <span class="keywordtype">void</span> <a class="code" href="classEllipsoidDecomp.html#a3a98cbbe53c7d641b12bc9bb5916d8d6">set_local_bbox</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; bbox) { local_bbox_ = bbox; }</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#ad880fd3bbaa9b5ccefcbc4317bbc68a5">   38</a></span>&#160; <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="classEllipsoidDecomp.html#ad880fd3bbaa9b5ccefcbc4317bbc68a5">get_path</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> path_; }</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#a111a2f74f8cc807d15e70e121bf03f7c">   41</a></span>&#160; <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Polyhedron&lt;Dim&gt;</a>&gt; <a class="code" href="classEllipsoidDecomp.html#a111a2f74f8cc807d15e70e121bf03f7c">get_polyhedrons</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> polyhedrons_; }</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#a3c8bf3ede2d7be93d74239cae76495e6">   44</a></span>&#160; <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Ellipsoid&lt;Dim&gt;</a>&gt; <a class="code" href="classEllipsoidDecomp.html#a3c8bf3ede2d7be93d74239cae76495e6">get_ellipsoids</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> ellipsoids_; }</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#aa43d19fea353dfd9466f729c0c38c581">   47</a></span>&#160; <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;LinearConstraint&lt;Dim&gt;</a>&gt; <a class="code" href="classEllipsoidDecomp.html#aa43d19fea353dfd9466f729c0c38c581">get_constraints</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;   <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;LinearConstraint&lt;Dim&gt;</a>&gt; constraints;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;   constraints.resize(polyhedrons_.size());</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;   <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; polyhedrons_.size(); i++){</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;     <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pt = (path_[i] + path_[i+1])/2;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;     constraints[i] = <a class="code" href="structLinearConstraint.html">LinearConstraint&lt;Dim&gt;</a>(pt, polyhedrons_[i].hyperplanes());</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;   }</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;   <span class="keywordflow">return</span> constraints;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160; }</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">   62</a></span>&#160; <span class="keywordtype">void</span> <a class="code" href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">dilate</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;path, <span class="keywordtype">double</span> offset_x = 0) {</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;   <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> N = path.size() - 1;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;   lines_.resize(N);</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;   ellipsoids_.resize(N);</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;   polyhedrons_.resize(N);</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;   <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; N; i++) {</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;     lines_[i] = std::make_shared&lt;LineSegment&lt;Dim&gt;&gt;(path[i], path[i+1]);</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;     lines_[i]-&gt;set_local_bbox(local_bbox_);</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;     lines_[i]-&gt;set_obs(obs_);</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;     lines_[i]-&gt;dilate(offset_x);</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;     ellipsoids_[i] = lines_[i]-&gt;get_ellipsoid();</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;     polyhedrons_[i] = lines_[i]-&gt;get_polyhedron();</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;   }</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;   path_ = path;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;   <span class="keywordflow">if</span>(global_bbox_min_.norm() != 0 || global_bbox_max_.norm() != 0) {</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;     <span class="keywordflow">for</span>(<span class="keyword">auto</span>&amp; it: polyhedrons_)</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;       add_global_bbox(it);</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;   }</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; }</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="keyword">protected</span>:</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> U = Dim&gt;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;   <span class="keyword">typename</span> std::enable_if&lt;U == 2&gt;::type</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;   add_global_bbox(<a class="code" href="structPolyhedron.html">Polyhedron&lt;Dim&gt;</a> &amp;Vs) {</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;     <span class="comment">//**** add bound along X, Y axis</span></div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;     <span class="comment">//*** X</span></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane2D</a>(<a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(global_bbox_max_(0), 0), <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(1, 0)));</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane2D</a>(<a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(global_bbox_min_(0), 0), <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(-1, 0)));</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;     <span class="comment">//*** Y</span></div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane2D</a>(<a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(0, global_bbox_max_(1)), <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(0, 1)));</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane2D</a>(<a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(0, global_bbox_min_(1)), <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a>(0, -1)));</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;   }</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> U = Dim&gt;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;   <span class="keyword">typename</span> std::enable_if&lt;U == 3&gt;::type</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;   add_global_bbox(<a class="code" href="structPolyhedron.html">Polyhedron&lt;Dim&gt;</a> &amp;Vs) {</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;     <span class="comment">//**** add bound along X, Y, Z axis</span></div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;     <span class="comment">//*** Z</span></div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, global_bbox_max_(2)), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, 1)));</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, global_bbox_min_(2)), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, -1)));</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;     <span class="comment">//*** X</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(global_bbox_max_(0), 0, 0), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(1, 0, 0)));</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(global_bbox_min_(0), 0, 0), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(-1, 0, 0)));</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;     <span class="comment">//*** Y</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, global_bbox_max_(1), 0), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 1, 0)));</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;     Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane3D</a>(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, global_bbox_max_(1), 0), <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, -1, 0)));</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;   }</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160; <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> path_;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> obs_;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Ellipsoid&lt;Dim&gt;</a>&gt; ellipsoids_;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Polyhedron&lt;Dim&gt;</a>&gt; polyhedrons_;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; std::vector&lt;std::shared_ptr&lt;LineSegment&lt;Dim&gt;&gt;&gt; lines_;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160; <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> local_bbox_{<a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Zero</a>()};</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160; <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> global_bbox_min_{<a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Zero</a>()}; <span class="comment">// bounding box params</span></div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> global_bbox_max_{<a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Zero</a>()};</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;};</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp&lt;2&gt;</a> <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp2D</a>;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp&lt;3&gt;</a> <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp3D</a>;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a74599b2a677a5186ef71a2a690c6171d"><div class="ttname"><a href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a></div><div class="ttdeci">vec_E&lt; Vecf&lt; N &gt;&gt; vec_Vecf</div><div class="ttdoc">Vector of Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:69</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_a3a98cbbe53c7d641b12bc9bb5916d8d6"><div class="ttname"><a href="classEllipsoidDecomp.html#a3a98cbbe53c7d641b12bc9bb5916d8d6">EllipsoidDecomp::set_local_bbox</a></div><div class="ttdeci">void set_local_bbox(const Vecf&lt; Dim &gt; &amp;bbox)</div><div class="ttdoc">Set dimension of bounding box. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:35</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_ac6e757930647fb499d1f1ffeef01b6a4"><div class="ttname"><a href="classEllipsoidDecomp.html#ac6e757930647fb499d1f1ffeef01b6a4">EllipsoidDecomp::EllipsoidDecomp</a></div><div class="ttdeci">EllipsoidDecomp()</div><div class="ttdoc">Simple constructor. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:20</div></div>
<div class="ttc" id="classEllipsoidDecomp_html"><div class="ttname"><a href="classEllipsoidDecomp.html">EllipsoidDecomp</a></div><div class="ttdoc">EllipsoidDecomp Class. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:17</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_aaf4731df44249fe6ed026efa12d6bf63"><div class="ttname"><a href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">EllipsoidDecomp::dilate</a></div><div class="ttdeci">void dilate(const vec_Vecf&lt; Dim &gt; &amp;path, double offset_x=0)</div><div class="ttdoc">Decomposition thread. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:62</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_a4c9617e1e2d94ead1ec921e9af7951d0"><div class="ttname"><a href="classEllipsoidDecomp.html#a4c9617e1e2d94ead1ec921e9af7951d0">EllipsoidDecomp::EllipsoidDecomp</a></div><div class="ttdeci">EllipsoidDecomp(const Vecf&lt; Dim &gt; &amp;origin, const Vecf&lt; Dim &gt; &amp;dim)</div><div class="ttdoc">Basic constructor. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:26</div></div>
<div class="ttc" id="structLinearConstraint_html"><div class="ttname"><a href="structLinearConstraint.html">LinearConstraint</a></div><div class="ttdoc">[A, b] for  </div><div class="ttdef"><b>Definition:</b> polyhedron.h:99</div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="line__segment_8h_html"><div class="ttname"><a href="line__segment_8h.html">line_segment.h</a></div><div class="ttdoc">LineSegment Class. </div></div>
<div class="ttc" id="structPolyhedron_html_a1efa5c7b822945d37c93a38667f8d04d"><div class="ttname"><a href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">Polyhedron::add</a></div><div class="ttdeci">void add(const Hyperplane&lt; Dim &gt; &amp;v)</div><div class="ttdoc">Append Hyperplane. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:49</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_aa43d19fea353dfd9466f729c0c38c581"><div class="ttname"><a href="classEllipsoidDecomp.html#aa43d19fea353dfd9466f729c0c38c581">EllipsoidDecomp::get_constraints</a></div><div class="ttdeci">vec_E&lt; LinearConstraint&lt; Dim &gt; &gt; get_constraints() const </div><div class="ttdoc">Get the constraints of SFC as . </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:47</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_a3c8bf3ede2d7be93d74239cae76495e6"><div class="ttname"><a href="classEllipsoidDecomp.html#a3c8bf3ede2d7be93d74239cae76495e6">EllipsoidDecomp::get_ellipsoids</a></div><div class="ttdeci">vec_E&lt; Ellipsoid&lt; Dim &gt; &gt; get_ellipsoids() const </div><div class="ttdoc">Get the ellipsoids. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:44</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_aa3ea99617acd7f626ac7e731be9bea9f"><div class="ttname"><a href="classEllipsoidDecomp.html#aa3ea99617acd7f626ac7e731be9bea9f">EllipsoidDecomp::set_obs</a></div><div class="ttdeci">void set_obs(const vec_Vecf&lt; Dim &gt; &amp;obs)</div><div class="ttdoc">Set obstacle points. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:32</div></div>
<div class="ttc" id="data__type_8h_html_afd53e073786661e24985c48b4ef92fcb"><div class="ttname"><a href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a></div><div class="ttdeci">Vecf&lt; 3 &gt; Vec3f</div><div class="ttdoc">Eigen 1D float vector of size 3. </div><div class="ttdef"><b>Definition:</b> data_type.h:79</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_ad880fd3bbaa9b5ccefcbc4317bbc68a5"><div class="ttname"><a href="classEllipsoidDecomp.html#ad880fd3bbaa9b5ccefcbc4317bbc68a5">EllipsoidDecomp::get_path</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; get_path() const </div><div class="ttdoc">Get the path that is used for dilation. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:38</div></div>
<div class="ttc" id="data__type_8h_html_a30c607180de5bc1b7c30f5cbaf9b188b"><div class="ttname"><a href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a></div><div class="ttdeci">std::vector&lt; T, Eigen::aligned_allocator&lt; T &gt;&gt; vec_E</div><div class="ttdoc">Pre-allocated std::vector for Eigen using vec_E. </div><div class="ttdef"><b>Definition:</b> data_type.h:54</div></div>
<div class="ttc" id="data__type_8h_html_a135c596de9b80bec15985876ebd5036e"><div class="ttname"><a href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a></div><div class="ttdeci">Vecf&lt; 2 &gt; Vec2f</div><div class="ttdoc">Eigen 1D float vector of size 2. </div><div class="ttdef"><b>Definition:</b> data_type.h:75</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_a111a2f74f8cc807d15e70e121bf03f7c"><div class="ttname"><a href="classEllipsoidDecomp.html#a111a2f74f8cc807d15e70e121bf03f7c">EllipsoidDecomp::get_polyhedrons</a></div><div class="ttdeci">vec_E&lt; Polyhedron&lt; Dim &gt; &gt; get_polyhedrons() const </div><div class="ttdoc">Get the Safe Flight Corridor. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:41</div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="structHyperplane_html"><div class="ttname"><a href="structHyperplane.html">Hyperplane</a></div><div class="ttdoc">Hyperplane class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:13</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
