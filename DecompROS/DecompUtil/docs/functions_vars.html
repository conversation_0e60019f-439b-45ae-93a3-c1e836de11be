<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Class Members - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_func.html"><span>Functions</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;<ul>
<li>ellipsoid_
: <a class="el" href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">DecompBase&lt; Dim &gt;</a>
</li>
<li>local_bbox_
: <a class="el" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">DecompBase&lt; Dim &gt;</a>
</li>
<li>n_
: <a class="el" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">Hyperplane&lt; Dim &gt;</a>
</li>
<li>obs_
: <a class="el" href="classDecompBase.html#af3afcc5ba0baa24fa1a52a08138fb67b">DecompBase&lt; Dim &gt;</a>
</li>
<li>p1_
: <a class="el" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">LineSegment&lt; Dim &gt;</a>
</li>
<li>p2_
: <a class="el" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">LineSegment&lt; Dim &gt;</a>
</li>
<li>p_
: <a class="el" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">Hyperplane&lt; Dim &gt;</a>
, <a class="el" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">SeedDecomp&lt; Dim &gt;</a>
</li>
<li>polyhedron_
: <a class="el" href="classDecompBase.html#a21a9039cefecfe2166dc5faf8ba86a68">DecompBase&lt; Dim &gt;</a>
</li>
<li>vs_
: <a class="el" href="structPolyhedron.html#ad65ac3518e69a9f99ba41d23f943034b">Polyhedron&lt; Dim &gt;</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
