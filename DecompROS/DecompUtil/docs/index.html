<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: MRSL Decomputil Library v1.0</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li class="current"><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">MRSL Decomputil Library v1.0 </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p><a href="https://app.wercker.com/project/byKey/89a66f8c94c00db95dc056bae099adb3"></a> </p><hr/>
<p> A header only c++ library for fast convex decomposition. In the basic pipeline, it implements ellipsoid based regional inflation to model free space from a given path inside a point cloud. Detials of the algorithm is proposed in <a href="http://ieeexplore.ieee.org/document/7839930/">"S. Liu, M. Watterson, K. Mohta, K. Sun, S. Bhattacharya, C.J. Taylor and V. Kumar. Planning Dynamically Feasible Trajectories for Quadrotors using Safe Flight Corridors in 3-D Complex Environments. ICRA 2017"</a>.</p>
<h2>Installation</h2>
<p>DecompUtil is a header only library, several test nodes are compiled during the installation. #### A) Simple cmake </p><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;$ mkdir build &amp;&amp; cd build &amp;&amp; cmake .. &amp;&amp; make</div></div><!-- fragment --><p>#### B) Using CATKIN (not recognizable by catkin_make) </p><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;$ cd mv decomp_util ~/catkin_ws/src</div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;$ cd ~/catkin_ws &amp; catkin_make_isolated -DCMAKE_BUILD_TYPE=Release</div></div><!-- fragment --><h4>CTest</h4>
<p>To check if everything is installed properlly: </p><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;$ make test</div></div><!-- fragment --><h4>Include in other projects:</h4>
<p>To link this lib properly, add following in the <code>CMakeLists.txt</code> </p><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;find_package(decomp_util REQUIRED)</div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;include_directories(${DECOMP_UTIL_INCLUDE_DIRS})</div></div><!-- fragment --><h2>Examples</h2>
<p>The examples of using <code>SeedDecomp2D</code>, <code>LineSegment2D</code>, <code>EllipsoidDecomp2D</code> and <code>IterativeDecomp2D</code> are plotted as followings:</p>
<table class="doxtable">
<tr>
<th align="left">SeedDecomp2D </th><th align="left">LineSegment2D </th><th align="left">EllipsoidDecomp2D </th><th align="left">IterativeDecomp2D  </th></tr>
<tr>
<td align="left"><div class="image">
<img src="./data/example1.png"  height="200"/>
</div>
 </td><td align="left"><div class="image">
<img src="./data/example2.png"  height="200"/>
</div>
 </td><td align="left"><div class="image">
<img src="./data/example3.png"  height="200"/>
</div>
 </td><td align="left"><div class="image">
<img src="./data/example4.png"  height="200"/>
</div>
 </td></tr>
</table>
<p>The corresponding code for testing can be found in the <code>test</code> folder.</p>
<h2>Doxygen</h2>
<p>For more details, please refer to <a href="https://sikang.github.io/DecompUtil/index.html">https://sikang.github.io/DecompUtil/index.html</a></p>
<h2>ROS</h2>
<p>The ROS wrapper for easier use of this package can be found in <a href="https://github.com/sikang/DecompROS.git"><code>DecompROS</code></a>. </p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
