<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: File Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li class="current"><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
  <div id="navrow3" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="globals.html"><span>All</span></a></li>
      <li><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_defs.html"><span>Macros</span></a></li>
    </ul>
  </div>
  <div id="navrow4" class="tabs3">
    <ul class="tablist">
      <li><a href="#index_a"><span>a</span></a></li>
      <li><a href="#index_c"><span>c</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_e"><span>e</span></a></li>
      <li><a href="#index_l"><span>l</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_q"><span>q</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li><a href="#index_t"><span>t</span></a></li>
      <li class="current"><a href="#index_v"><span>v</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented file members with links to the documentation:</div>

<h3><a class="anchor" id="index_a"></a>- a -</h3><ul>
<li>Aff2f
: <a class="el" href="data__type_8h.html#a3b95b3d43bb8590852e0e74abaff3c6a">data_type.h</a>
</li>
<li>Aff3f
: <a class="el" href="data__type_8h.html#a99980a710976449f0a9d3d2ae8b8be87">data_type.h</a>
</li>
<li>ANSI_COLOR_BLUE
: <a class="el" href="data__type_8h.html#aca16e6a49eb51333c5fd3eee19487315">data_type.h</a>
</li>
<li>ANSI_COLOR_CYAN
: <a class="el" href="data__type_8h.html#a8d0b0043e152438bb39b918a1f98c65f">data_type.h</a>
</li>
<li>ANSI_COLOR_GREEN
: <a class="el" href="data__type_8h.html#a966c72d8d733c7734c6c784753d894c7">data_type.h</a>
</li>
<li>ANSI_COLOR_MAGENTA
: <a class="el" href="data__type_8h.html#acb30614ba1535da5b9d0c490b3c10515">data_type.h</a>
</li>
<li>ANSI_COLOR_RED
: <a class="el" href="data__type_8h.html#a34995b955465f6bbb37c359173d50477">data_type.h</a>
</li>
<li>ANSI_COLOR_RESET
: <a class="el" href="data__type_8h.html#a92a364c2b863dde1a024a77eac2a5b3b">data_type.h</a>
</li>
<li>ANSI_COLOR_YELLOW
: <a class="el" href="data__type_8h.html#a5a123b382640b3aa65dd5db386002fbc">data_type.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_c"></a>- c -</h3><ul>
<li>cal_vertices()
: <a class="el" href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">geometric_utils.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_d"></a>- d -</h3><ul>
<li>decimal_t
: <a class="el" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">data_type.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_e"></a>- e -</h3><ul>
<li>eigen_value()
: <a class="el" href="geometric__utils_8h.html#a0ce1d8245227b22ff06fe09915db19c5">geometric_utils.h</a>
</li>
<li>epsilon_
: <a class="el" href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">data_type.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_l"></a>- l -</h3><ul>
<li>line_intersect()
: <a class="el" href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">geometric_utils.h</a>
</li>
<li>line_intersects()
: <a class="el" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">geometric_utils.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_m"></a>- m -</h3><ul>
<li>Mat2f
: <a class="el" href="data__type_8h.html#a5503e9ed3faaa114b9611829fb322981">data_type.h</a>
</li>
<li>Mat3f
: <a class="el" href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">data_type.h</a>
</li>
<li>Mat4f
: <a class="el" href="data__type_8h.html#ad2b84927631f460dbf9862f63d624e09">data_type.h</a>
</li>
<li>Mat6f
: <a class="el" href="data__type_8h.html#a09f49eaed626a21b73aaee4c33e6fa45">data_type.h</a>
</li>
<li>MatD2f
: <a class="el" href="data__type_8h.html#a2de422e2e68d8cecbfb1814e07e9a292">data_type.h</a>
</li>
<li>MatD3f
: <a class="el" href="data__type_8h.html#a9c901cc0e1d9f03aab4aa4ea587517da">data_type.h</a>
</li>
<li>MatDf
: <a class="el" href="data__type_8h.html#ab13729f7d29cc8284965c7c42129a45f">data_type.h</a>
</li>
<li>MatDNf
: <a class="el" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">data_type.h</a>
</li>
<li>Matf
: <a class="el" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">data_type.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_q"></a>- q -</h3><ul>
<li>Quatf
: <a class="el" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">data_type.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_s"></a>- s -</h3><ul>
<li>sort_pts()
: <a class="el" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">geometric_utils.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_t"></a>- t -</h3><ul>
<li>total_distance()
: <a class="el" href="data__utils_8h.html#a3f82ae87bcf143405fd986be6ca44a68">data_utils.h</a>
</li>
<li>total_distance3f
: <a class="el" href="data__utils_8h.html#a188bab5f6177f4853e7bff0d5c61cfe6">data_utils.h</a>
</li>
<li>total_distance3i
: <a class="el" href="data__utils_8h.html#aca4035bd3287a21e2e0a8e7a5a0357f5">data_utils.h</a>
</li>
<li>transform_vec()
: <a class="el" href="data__utils_8h.html#ac3745ddcc3002193bbcd5caab64cd1df">data_utils.h</a>
</li>
<li>transform_vec3
: <a class="el" href="data__utils_8h.html#a79960a9e7c7bad6ec5641ed1729ad8d0">data_utils.h</a>
</li>
</ul>


<h3><a class="anchor" id="index_v"></a>- v -</h3><ul>
<li>vec2_to_rotation()
: <a class="el" href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">geometric_utils.h</a>
</li>
<li>Vec2f
: <a class="el" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">data_type.h</a>
</li>
<li>Vec2i
: <a class="el" href="data__type_8h.html#a08412347a26b00366a9d576672cce68d">data_type.h</a>
</li>
<li>Vec3f
: <a class="el" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">data_type.h</a>
</li>
<li>Vec3i
: <a class="el" href="data__type_8h.html#a27cc7cd350b4919c77a6803461feb516">data_type.h</a>
</li>
<li>Vec4f
: <a class="el" href="data__type_8h.html#a885809dc84c0c55d44fe4836f5cfa39b">data_type.h</a>
</li>
<li>Vec6f
: <a class="el" href="data__type_8h.html#a940b1af907878e3cb6d2f7694730ee01">data_type.h</a>
</li>
<li>vec_E
: <a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">data_type.h</a>
</li>
<li>vec_Vec2f
: <a class="el" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">data_type.h</a>
</li>
<li>vec_Vec2i
: <a class="el" href="data__type_8h.html#a79168c9f029798876a1093e0e53bc36b">data_type.h</a>
</li>
<li>vec_Vec3f
: <a class="el" href="data__type_8h.html#a62c46ed3e3ab6773b30439f9be38290b">data_type.h</a>
</li>
<li>vec_Vec3i
: <a class="el" href="data__type_8h.html#aab99928bb9e8a58f0aeaf5d31c4866e4">data_type.h</a>
</li>
<li>vec_Vecf
: <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">data_type.h</a>
</li>
<li>vec_Veci
: <a class="el" href="data__type_8h.html#ab0b0b0b007b0ba902fb040a70b3744e6">data_type.h</a>
</li>
<li>VecDf
: <a class="el" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">data_type.h</a>
</li>
<li>Vecf
: <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">data_type.h</a>
</li>
<li>Veci
: <a class="el" href="data__type_8h.html#ac208833a6aa7ec29f39eb5ae142aaede">data_type.h</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
