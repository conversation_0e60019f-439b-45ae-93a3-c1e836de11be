<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_geometry/polyhedron.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_0156596343f07f423e58f27e9acfceb9.html">decomp_geometry</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">polyhedron.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#ifndef DECOMP_POLYGON_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#define DECOMP_POLYGON_H</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;</div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="data__type_8h.html">decomp_basis/data_type.h</a>&gt;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00013"></a><span class="lineno"><a class="line" href="structHyperplane.html">   13</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structHyperplane.html">Hyperplane</a> {</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;  <a class="code" href="structHyperplane.html">Hyperplane</a>() {}</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;  <a class="code" href="structHyperplane.html">Hyperplane</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; p, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; n) : <a class="code" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">p_</a>(p), <a class="code" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">n_</a>(n) {}</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno"><a class="line" href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">   18</a></span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> <a class="code" href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">signed_dist</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt)<span class="keyword"> const </span>{</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">n_</a>.dot(pt - <a class="code" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">p_</a>);</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;  }</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div><div class="line"><a name="l00023"></a><span class="lineno"><a class="line" href="structHyperplane.html#add0b2123e1bb1923e25cddfb7dd62804">   23</a></span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> <a class="code" href="structHyperplane.html#add0b2123e1bb1923e25cddfb7dd62804">dist</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt)<span class="keyword"> const </span>{</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    <span class="keywordflow">return</span> std::abs(<a class="code" href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">signed_dist</a>(pt));</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;  }</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno"><a class="line" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">   28</a></span>&#160;  <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">p_</a>;</div><div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">   30</a></span>&#160;  <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">n_</a>;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;};</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structHyperplane.html">Hyperplane&lt;2&gt;</a> <a class="code" href="structHyperplane.html">Hyperplane2D</a>;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structHyperplane.html">Hyperplane&lt;3&gt;</a> <a class="code" href="structHyperplane.html">Hyperplane3D</a>;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="structPolyhedron.html">   41</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structPolyhedron.html">Polyhedron</a> {</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="structPolyhedron.html#a8b9a5d2d44059c016486be3e3e05ddeb">   43</a></span>&#160;  <a class="code" href="structPolyhedron.html#a8b9a5d2d44059c016486be3e3e05ddeb">Polyhedron</a>() {}</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="structPolyhedron.html#a54b2d009a7392830934f04f5226fc591">   45</a></span>&#160;  <a class="code" href="structPolyhedron.html#a54b2d009a7392830934f04f5226fc591">Polyhedron</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt;<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>&gt;&amp; vs) : vs_(vs) {}</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">   49</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<span class="keyword">const</span> <a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>&amp; v) {</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    vs_.push_back(v);</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  }</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">   54</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">inside</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt)<span class="keyword"> const </span>{</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span>&amp; v : vs_) {</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;      <span class="keywordflow">if</span> (v.signed_dist(pt) &gt; <a class="code" href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a>) {</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="comment">//printf(&quot;rejected pt: (%f, %f), d: %f\n&quot;,pt(0), pt(1), v.signed_dist(pt));</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      }</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    }</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">true</span>;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  }</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">   65</a></span>&#160;  <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">points_inside</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;O)<span class="keyword"> const </span>{</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> new_O;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it : O) {</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;      <span class="keywordflow">if</span> (inside(it))</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        new_O.push_back(it);</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    }</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="keywordflow">return</span> new_O;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  }</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structPolyhedron.html#aaf89b0bd80d59b3ff4c7f40b3872e2f3">   75</a></span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;std::pair&lt;Vecf&lt;Dim&gt;</a>, <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&gt;&gt; <a class="code" href="structPolyhedron.html#aaf89b0bd80d59b3ff4c7f40b3872e2f3">cal_normals</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;std::pair&lt;Vecf&lt;Dim&gt;</a>, <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&gt;&gt; ns(vs_.size());</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; vs_.size(); i++)</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;      ns[i] = std::make_pair(vs_[i].<a class="code" href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">p_</a>, vs_[i].<a class="code" href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">n_</a>); <span class="comment">// fist is point, second is normal</span></div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    <span class="keywordflow">return</span> ns;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  }</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">   83</a></span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Hyperplane&lt;Dim&gt;</a>&gt; <a class="code" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">hyperplanes</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    <span class="keywordflow">return</span> vs_;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  }</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="structPolyhedron.html#ad65ac3518e69a9f99ba41d23f943034b">   88</a></span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;Hyperplane&lt;Dim&gt;</a>&gt; <a class="code" href="structPolyhedron.html#ad65ac3518e69a9f99ba41d23f943034b">vs_</a>; <span class="comment">// normal must go outside</span></div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;};</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structPolyhedron.html">Polyhedron&lt;2&gt;</a> <a class="code" href="structPolyhedron.html">Polyhedron2D</a>;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structPolyhedron.html">Polyhedron&lt;3&gt;</a> <a class="code" href="structPolyhedron.html">Polyhedron3D</a>;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structLinearConstraint.html">   99</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structLinearConstraint.html">LinearConstraint</a> {</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9">  101</a></span>&#160;  <a class="code" href="structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9">LinearConstraint</a>() {}</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structLinearConstraint.html#aa05fed6e3ac2a531078ec96c174595c7">  103</a></span>&#160;  <a class="code" href="structLinearConstraint.html#aa05fed6e3ac2a531078ec96c174595c7">LinearConstraint</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf&lt;Dim&gt;</a>&amp; A, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a>&amp; b) : A_(A), b_(b) {}</div><div class="line"><a name="l00109"></a><span class="lineno"><a class="line" href="structLinearConstraint.html#a81d1602ce0df605f5cb3746ad56c91fd">  109</a></span>&#160;    <a class="code" href="structLinearConstraint.html#a81d1602ce0df605f5cb3746ad56c91fd">LinearConstraint</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> p0, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt;<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>&gt;&amp; vs) {</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> size = vs.size();</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        <a class="code" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf&lt;Dim&gt;</a> A(size, Dim);</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <a class="code" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a> b(size);</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; size; i++) {</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;            <span class="keyword">const</span> <span class="keyword">auto</span> n = vs[i].n_;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;            <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> c = vs[i].p_.dot(n);</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;            <span class="keywordflow">if</span> (n.dot(p0) - c &gt; 0) {</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                n = -n;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                c = -c;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;            }</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;            A.row(i) = n;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;            b(i) = c;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        }</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        A_ = A;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        b_ = b;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    }</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="structLinearConstraint.html#a23779a58e2a1094971cdcceeac3f7f74">  130</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="structLinearConstraint.html#a23779a58e2a1094971cdcceeac3f7f74">inside</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;pt) {</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <a class="code" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a> d = A_ * pt - b_;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; d.rows(); i++) {</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;      <span class="keywordflow">if</span> (d(i) &gt; 0)</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    }</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">true</span>;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  }</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  <a class="code" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf&lt;Dim&gt;</a> A_;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <a class="code" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a> b_;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;};</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structLinearConstraint.html">LinearConstraint&lt;2&gt;</a> <a class="code" href="structLinearConstraint.html">LinearConstraint2D</a>;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structLinearConstraint.html">LinearConstraint&lt;3&gt;</a> <a class="code" href="structLinearConstraint.html">LinearConstraint3D</a>;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a74599b2a677a5186ef71a2a690c6171d"><div class="ttname"><a href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a></div><div class="ttdeci">vec_E&lt; Vecf&lt; N &gt;&gt; vec_Vecf</div><div class="ttdoc">Vector of Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:69</div></div>
<div class="ttc" id="structLinearConstraint_html_a23779a58e2a1094971cdcceeac3f7f74"><div class="ttname"><a href="structLinearConstraint.html#a23779a58e2a1094971cdcceeac3f7f74">LinearConstraint::inside</a></div><div class="ttdeci">bool inside(const Vecf&lt; Dim &gt; &amp;pt)</div><div class="ttdoc">Check if the point is inside polyhedron using linear constraint. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:130</div></div>
<div class="ttc" id="structPolyhedron_html_a28e5b0eebab18fb8d54fb021333363ce"><div class="ttname"><a href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">Polyhedron::inside</a></div><div class="ttdeci">bool inside(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Check if the point is inside polyhedron, non-exclusive. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:54</div></div>
<div class="ttc" id="structHyperplane_html_aa25c61b6fd4a9cdf8987324897f43939"><div class="ttname"><a href="structHyperplane.html#aa25c61b6fd4a9cdf8987324897f43939">Hyperplane::signed_dist</a></div><div class="ttdeci">decimal_t signed_dist(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Calculate the signed distance from point. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:18</div></div>
<div class="ttc" id="structLinearConstraint_html_a81d1602ce0df605f5cb3746ad56c91fd"><div class="ttname"><a href="structLinearConstraint.html#a81d1602ce0df605f5cb3746ad56c91fd">LinearConstraint::LinearConstraint</a></div><div class="ttdeci">LinearConstraint(const Vecf&lt; Dim &gt; p0, const vec_E&lt; Hyperplane&lt; Dim &gt;&gt; &amp;vs)</div><div class="ttdoc">Construct from a inside point and hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:109</div></div>
<div class="ttc" id="structLinearConstraint_html"><div class="ttname"><a href="structLinearConstraint.html">LinearConstraint</a></div><div class="ttdoc">[A, b] for  </div><div class="ttdef"><b>Definition:</b> polyhedron.h:99</div></div>
<div class="ttc" id="structHyperplane_html_afec3414bc825315a9fd3af4001ee5933"><div class="ttname"><a href="structHyperplane.html#afec3414bc825315a9fd3af4001ee5933">Hyperplane::p_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; p_</div><div class="ttdoc">Point on the plane. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:28</div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="data__type_8h_html_a81ebeac2c4a6e9be147beb487779e9b5"><div class="ttname"><a href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a></div><div class="ttdeci">constexpr decimal_t epsilon_</div><div class="ttdoc">Compensate for numerical error. </div><div class="ttdef"><b>Definition:</b> data_type.h:126</div></div>
<div class="ttc" id="structPolyhedron_html_a1efa5c7b822945d37c93a38667f8d04d"><div class="ttname"><a href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">Polyhedron::add</a></div><div class="ttdeci">void add(const Hyperplane&lt; Dim &gt; &amp;v)</div><div class="ttdoc">Append Hyperplane. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:49</div></div>
<div class="ttc" id="data__type_8h_html_af9c7300efe3567726a373210d4dbc046"><div class="ttname"><a href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a></div><div class="ttdeci">Vecf&lt; Eigen::Dynamic &gt; VecDf</div><div class="ttdoc">Dynamic Nx1 Eigen float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:106</div></div>
<div class="ttc" id="structPolyhedron_html_ac91f6e4f56758d09dd36b139249ae566"><div class="ttname"><a href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">Polyhedron::points_inside</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; points_inside(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Calculate points inside polyhedron, non-exclusive. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:65</div></div>
<div class="ttc" id="structPolyhedron_html_a92118114a7bce0e799ff1c587fff04a4"><div class="ttname"><a href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">Polyhedron::hyperplanes</a></div><div class="ttdeci">vec_E&lt; Hyperplane&lt; Dim &gt; &gt; hyperplanes() const </div><div class="ttdoc">Get the hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:83</div></div>
<div class="ttc" id="structPolyhedron_html_ad65ac3518e69a9f99ba41d23f943034b"><div class="ttname"><a href="structPolyhedron.html#ad65ac3518e69a9f99ba41d23f943034b">Polyhedron::vs_</a></div><div class="ttdeci">vec_E&lt; Hyperplane&lt; Dim &gt; &gt; vs_</div><div class="ttdoc">Hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:88</div></div>
<div class="ttc" id="data__type_8h_html_a44c975fba9ebd61e295d78215b6569c3"><div class="ttname"><a href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, Eigen::Dynamic, N &gt; MatDNf</div><div class="ttdoc">MxN Eigen matrix with M unknown. </div><div class="ttdef"><b>Definition:</b> data_type.h:66</div></div>
<div class="ttc" id="structHyperplane_html_add0b2123e1bb1923e25cddfb7dd62804"><div class="ttname"><a href="structHyperplane.html#add0b2123e1bb1923e25cddfb7dd62804">Hyperplane::dist</a></div><div class="ttdeci">decimal_t dist(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Calculate the distance from point. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:23</div></div>
<div class="ttc" id="structPolyhedron_html_a8b9a5d2d44059c016486be3e3e05ddeb"><div class="ttname"><a href="structPolyhedron.html#a8b9a5d2d44059c016486be3e3e05ddeb">Polyhedron::Polyhedron</a></div><div class="ttdeci">Polyhedron()</div><div class="ttdoc">Null constructor. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:43</div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="data__type_8h_html_a30c607180de5bc1b7c30f5cbaf9b188b"><div class="ttname"><a href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a></div><div class="ttdeci">std::vector&lt; T, Eigen::aligned_allocator&lt; T &gt;&gt; vec_E</div><div class="ttdoc">Pre-allocated std::vector for Eigen using vec_E. </div><div class="ttdef"><b>Definition:</b> data_type.h:54</div></div>
<div class="ttc" id="structHyperplane_html_a6d2871b9f869a49794b72ff433247030"><div class="ttname"><a href="structHyperplane.html#a6d2871b9f869a49794b72ff433247030">Hyperplane::n_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; n_</div><div class="ttdoc">Normal of the plane, directional. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:30</div></div>
<div class="ttc" id="data__type_8h_html"><div class="ttname"><a href="data__type_8h.html">data_type.h</a></div><div class="ttdoc">Defines all data types used in this lib. </div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="structHyperplane_html"><div class="ttname"><a href="structHyperplane.html">Hyperplane</a></div><div class="ttdoc">Hyperplane class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:13</div></div>
<div class="ttc" id="structPolyhedron_html_a54b2d009a7392830934f04f5226fc591"><div class="ttname"><a href="structPolyhedron.html#a54b2d009a7392830934f04f5226fc591">Polyhedron::Polyhedron</a></div><div class="ttdeci">Polyhedron(const vec_E&lt; Hyperplane&lt; Dim &gt;&gt; &amp;vs)</div><div class="ttdoc">Construct from Hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:45</div></div>
<div class="ttc" id="structPolyhedron_html_aaf89b0bd80d59b3ff4c7f40b3872e2f3"><div class="ttname"><a href="structPolyhedron.html#aaf89b0bd80d59b3ff4c7f40b3872e2f3">Polyhedron::cal_normals</a></div><div class="ttdeci">vec_E&lt; std::pair&lt; Vecf&lt; Dim &gt;, Vecf&lt; Dim &gt; &gt; &gt; cal_normals() const </div><div class="ttdoc">Calculate normals, used for visualization. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:75</div></div>
<div class="ttc" id="structLinearConstraint_html_aa05fed6e3ac2a531078ec96c174595c7"><div class="ttname"><a href="structLinearConstraint.html#aa05fed6e3ac2a531078ec96c174595c7">LinearConstraint::LinearConstraint</a></div><div class="ttdeci">LinearConstraint(const MatDNf&lt; Dim &gt; &amp;A, const VecDf &amp;b)</div><div class="ttdoc">Construct from  directly, s.t . </div><div class="ttdef"><b>Definition:</b> polyhedron.h:103</div></div>
<div class="ttc" id="structLinearConstraint_html_ad089df15de12c20098e34f8497212ed9"><div class="ttname"><a href="structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9">LinearConstraint::LinearConstraint</a></div><div class="ttdeci">LinearConstraint()</div><div class="ttdoc">Null constructor. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:101</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
