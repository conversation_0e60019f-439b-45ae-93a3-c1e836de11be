<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_util/seed_decomp.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_75993fee8576b97e3d9a8476c1772d17.html">decomp_util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">seed_decomp.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="seed__decomp_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#ifndef SEED_DECOMP_H</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define SEED_DECOMP_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="decomp__base_8h.html">decomp_util/decomp_base.h</a>&gt;</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="classSeedDecomp.html">   16</a></span>&#160;<span class="keyword">class </span><a class="code" href="classSeedDecomp.html">SeedDecomp</a> : <span class="keyword">public</span> <a class="code" href="classDecompBase.html">DecompBase</a>&lt;Dim&gt; {</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00019"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#ac4cb38597f35ff4b1e27a225436d54dd">   19</a></span>&#160;    <a class="code" href="classSeedDecomp.html#ac4cb38597f35ff4b1e27a225436d54dd">SeedDecomp</a>() {};</div><div class="line"><a name="l00025"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#a4e0d038e09633d1892f0a184a439c4ba">   25</a></span>&#160;    <a class="code" href="classSeedDecomp.html#a4e0d038e09633d1892f0a184a439c4ba">SeedDecomp</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;p) : <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a>(p) {}</div><div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#a94db31b5d02e4ca19f32c3a5bacc25a2">   30</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classSeedDecomp.html#a94db31b5d02e4ca19f32c3a5bacc25a2">dilate</a>(<a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> radius) {</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;      this-&gt;<a class="code" href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">ellipsoid_</a> = <a class="code" href="structEllipsoid.html">Ellipsoid&lt;Dim&gt;</a>(radius * <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;::Identity</a>(), <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a>);</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;      this-&gt;find_polyhedron();</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;      <a class="code" href="classSeedDecomp.html#a0de774b2c94a41f84a6e73a8eefd8e8b">add_local_bbox</a>(this-&gt;<a class="code" href="classDecompBase.html#a21a9039cefecfe2166dc5faf8ba86a68">polyhedron_</a>);</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    }</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#a06743828a81c150c7368587ee7924e98">   37</a></span>&#160;    <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="classSeedDecomp.html#a06743828a81c150c7368587ee7924e98">get_seed</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;      <span class="keywordflow">return</span> <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a>;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    }</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;  <span class="keyword">protected</span>:</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#a0de774b2c94a41f84a6e73a8eefd8e8b">   43</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classSeedDecomp.html#a0de774b2c94a41f84a6e73a8eefd8e8b">add_local_bbox</a>(<a class="code" href="structPolyhedron.html">Polyhedron&lt;Dim&gt;</a> &amp;Vs) {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;      <span class="keywordflow">if</span>(this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>.norm() == 0)</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        <span class="keywordflow">return</span>;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;      <span class="comment">//**** virtual walls x-y-z</span></div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::UnitX</a>();</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir_h = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::UnitY</a>();</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp1 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> + dir_h * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(1);</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp2 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> - dir_h * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(1);</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp1, dir_h));</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp2, -dir_h));</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;      <span class="comment">// along y</span></div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp3 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> + dir * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(0);</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp4 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> - dir * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(0);</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp3, dir));</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp4, -dir));</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      <span class="comment">// along z</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      <span class="keywordflow">if</span>(Dim &gt; 2) {</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir_v = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::UnitZ</a>();</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp5 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> + dir_v * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(2);</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp6 = <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a> - dir_v * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(2);</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp5, dir_v));</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp6, -dir_v));</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;      }</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    }</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">   73</a></span>&#160;    <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">p_</a>;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;};</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classSeedDecomp.html">SeedDecomp&lt;2&gt;</a> <a class="code" href="classSeedDecomp.html">SeedDecomp2D</a>;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classSeedDecomp.html">SeedDecomp&lt;3&gt;</a> <a class="code" href="classSeedDecomp.html">SeedDecomp3D</a>;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="classDecompBase_html"><div class="ttname"><a href="classDecompBase.html">DecompBase</a></div><div class="ttdoc">Line Segment Class. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:18</div></div>
<div class="ttc" id="classDecompBase_html_a8af5e7a5c407102df57089253b35c44a"><div class="ttname"><a href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">DecompBase::local_bbox_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; local_bbox_</div><div class="ttdoc">Local bounding box along the line segment. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:94</div></div>
<div class="ttc" id="classSeedDecomp_html_a94db31b5d02e4ca19f32c3a5bacc25a2"><div class="ttname"><a href="classSeedDecomp.html#a94db31b5d02e4ca19f32c3a5bacc25a2">SeedDecomp::dilate</a></div><div class="ttdeci">void dilate(decimal_t radius)</div><div class="ttdoc">Inflate the seed with a sphere. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:30</div></div>
<div class="ttc" id="classDecompBase_html_a21a9039cefecfe2166dc5faf8ba86a68"><div class="ttname"><a href="classDecompBase.html#a21a9039cefecfe2166dc5faf8ba86a68">DecompBase::polyhedron_</a></div><div class="ttdeci">Polyhedron&lt; Dim &gt; polyhedron_</div><div class="ttdoc">Output polyhedron. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:91</div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="structEllipsoid_html"><div class="ttname"><a href="structEllipsoid.html">Ellipsoid</a></div><div class="ttdef"><b>Definition:</b> ellipsoid.h:14</div></div>
<div class="ttc" id="classSeedDecomp_html_a4e0d038e09633d1892f0a184a439c4ba"><div class="ttname"><a href="classSeedDecomp.html#a4e0d038e09633d1892f0a184a439c4ba">SeedDecomp::SeedDecomp</a></div><div class="ttdeci">SeedDecomp(const Vecf&lt; Dim &gt; &amp;p)</div><div class="ttdoc">Basic constructor. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:25</div></div>
<div class="ttc" id="structPolyhedron_html_a1efa5c7b822945d37c93a38667f8d04d"><div class="ttname"><a href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">Polyhedron::add</a></div><div class="ttdeci">void add(const Hyperplane&lt; Dim &gt; &amp;v)</div><div class="ttdoc">Append Hyperplane. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:49</div></div>
<div class="ttc" id="classSeedDecomp_html_a0de774b2c94a41f84a6e73a8eefd8e8b"><div class="ttname"><a href="classSeedDecomp.html#a0de774b2c94a41f84a6e73a8eefd8e8b">SeedDecomp::add_local_bbox</a></div><div class="ttdeci">void add_local_bbox(Polyhedron&lt; Dim &gt; &amp;Vs)</div><div class="ttdoc">Add the bounding box. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:43</div></div>
<div class="ttc" id="classSeedDecomp_html_ac4cb38597f35ff4b1e27a225436d54dd"><div class="ttname"><a href="classSeedDecomp.html#ac4cb38597f35ff4b1e27a225436d54dd">SeedDecomp::SeedDecomp</a></div><div class="ttdeci">SeedDecomp()</div><div class="ttdoc">Simple constructor. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:19</div></div>
<div class="ttc" id="data__type_8h_html_a1eeda0bad4efd3be8cb2da1941982410"><div class="ttname"><a href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, M, N &gt; Matf</div><div class="ttdoc">MxN Eigen matrix. </div><div class="ttdef"><b>Definition:</b> data_type.h:63</div></div>
<div class="ttc" id="decomp__base_8h_html"><div class="ttname"><a href="decomp__base_8h.html">decomp_base.h</a></div><div class="ttdoc">Decomp Base Class. </div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="classSeedDecomp_html_af9c8208721464dc53b7c2f1c1020c0a9"><div class="ttname"><a href="classSeedDecomp.html#af9c8208721464dc53b7c2f1c1020c0a9">SeedDecomp::p_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; p_</div><div class="ttdoc">Seed location. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:73</div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="structHyperplane_html"><div class="ttname"><a href="structHyperplane.html">Hyperplane</a></div><div class="ttdoc">Hyperplane class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:13</div></div>
<div class="ttc" id="classDecompBase_html_a58c41c590f6c92d9fa0886d4d735d7a1"><div class="ttname"><a href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">DecompBase::ellipsoid_</a></div><div class="ttdeci">Ellipsoid&lt; Dim &gt; ellipsoid_</div><div class="ttdoc">Output ellipsoid. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:89</div></div>
<div class="ttc" id="classSeedDecomp_html"><div class="ttname"><a href="classSeedDecomp.html">SeedDecomp</a></div><div class="ttdoc">Seed Decomp Class. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:16</div></div>
<div class="ttc" id="classSeedDecomp_html_a06743828a81c150c7368587ee7924e98"><div class="ttname"><a href="classSeedDecomp.html#a06743828a81c150c7368587ee7924e98">SeedDecomp::get_seed</a></div><div class="ttdeci">Vecf&lt; Dim &gt; get_seed() const </div><div class="ttdoc">Get the center. </div><div class="ttdef"><b>Definition:</b> seed_decomp.h:37</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
