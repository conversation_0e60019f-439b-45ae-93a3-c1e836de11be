<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: File List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li class="current"><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">File List</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all documented files with brief descriptions:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span id="img_0_" class="iconfopen" onclick="toggleFolder('0_')">&#160;</span><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html" target="_self">include</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_0_" class="arrow" onclick="toggleFolder('0_0_')">&#9660;</span><span id="img_0_0_" class="iconfopen" onclick="toggleFolder('0_0_')">&#160;</span><a class="el" href="dir_0ea7c0ecff050f14fbfcb8a22f95278f.html" target="_self">decomp_basis</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="data__type_8h_source.html"><span class="icondoc"></span></a><a class="el" href="data__type_8h.html" target="_self">data_type.h</a></td><td class="desc">Defines all data types used in this lib </td></tr>
<tr id="row_0_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="data__utils_8h_source.html"><span class="icondoc"></span></a><a class="el" href="data__utils_8h.html" target="_self">data_utils.h</a></td><td class="desc">Provide a few widely used function for basic type </td></tr>
<tr id="row_0_1_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_1_" class="arrow" onclick="toggleFolder('0_1_')">&#9660;</span><span id="img_0_1_" class="iconfopen" onclick="toggleFolder('0_1_')">&#160;</span><a class="el" href="dir_0156596343f07f423e58f27e9acfceb9.html" target="_self">decomp_geometry</a></td><td class="desc"></td></tr>
<tr id="row_0_1_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="ellipsoid_8h_source.html"><span class="icondoc"></span></a><a class="el" href="ellipsoid_8h.html" target="_self">ellipsoid.h</a></td><td class="desc"><a class="el" href="structEllipsoid.html">Ellipsoid</a> class </td></tr>
<tr id="row_0_1_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="geometric__utils_8h_source.html"><span class="icondoc"></span></a><a class="el" href="geometric__utils_8h.html" target="_self">geometric_utils.h</a></td><td class="desc">Basic geometry utils </td></tr>
<tr id="row_0_1_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="polyhedron_8h_source.html"><span class="icondoc"></span></a><b>polyhedron.h</b></td><td class="desc"></td></tr>
<tr id="row_0_2_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_2_" class="arrow" onclick="toggleFolder('0_2_')">&#9660;</span><span id="img_0_2_" class="iconfopen" onclick="toggleFolder('0_2_')">&#160;</span><a class="el" href="dir_75993fee8576b97e3d9a8476c1772d17.html" target="_self">decomp_util</a></td><td class="desc"></td></tr>
<tr id="row_0_2_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="decomp__base_8h_source.html"><span class="icondoc"></span></a><a class="el" href="decomp__base_8h.html" target="_self">decomp_base.h</a></td><td class="desc">Decomp Base Class </td></tr>
<tr id="row_0_2_1_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="ellipsoid__decomp_8h_source.html"><span class="icondoc"></span></a><a class="el" href="ellipsoid__decomp_8h.html" target="_self">ellipsoid_decomp.h</a></td><td class="desc"><a class="el" href="classEllipsoidDecomp.html" title="EllipsoidDecomp Class. ">EllipsoidDecomp</a> Class </td></tr>
<tr id="row_0_2_2_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="iterative__decomp_8h_source.html"><span class="icondoc"></span></a><a class="el" href="iterative__decomp_8h.html" target="_self">iterative_decomp.h</a></td><td class="desc"><a class="el" href="classIterativeDecomp.html" title="IterativeDecomp Class. ">IterativeDecomp</a> Class </td></tr>
<tr id="row_0_2_3_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="line__segment_8h_source.html"><span class="icondoc"></span></a><a class="el" href="line__segment_8h.html" target="_self">line_segment.h</a></td><td class="desc"><a class="el" href="classLineSegment.html" title="Line Segment Class. ">LineSegment</a> Class </td></tr>
<tr id="row_0_2_4_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="seed__decomp_8h_source.html"><span class="icondoc"></span></a><a class="el" href="seed__decomp_8h.html" target="_self">seed_decomp.h</a></td><td class="desc"><a class="el" href="classSeedDecomp.html" title="Seed Decomp Class. ">SeedDecomp</a> Class </td></tr>
<tr id="row_1_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_1_" class="arrow" onclick="toggleFolder('1_')">&#9660;</span><span id="img_1_" class="iconfopen" onclick="toggleFolder('1_')">&#160;</span><a class="el" href="dir_13e138d54eb8818da29c3992edef070a.html" target="_self">test</a></td><td class="desc"></td></tr>
<tr id="row_1_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><a href="txt__reader_8hpp_source.html"><span class="icondoc"></span></a><b>txt_reader.hpp</b></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
