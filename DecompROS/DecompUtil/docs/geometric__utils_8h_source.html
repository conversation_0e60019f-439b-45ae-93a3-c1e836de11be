<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_geometry/geometric_utils.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_0156596343f07f423e58f27e9acfceb9.html">decomp_geometry</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">geometric_utils.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="geometric__utils_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#ifndef DECOMP_GEOMETRIC_UTILS_H</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define DECOMP_GEOMETRIC_UTILS_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="data__utils_8h.html">decomp_basis/data_utils.h</a>&gt;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;Eigen/Eigenvalues&gt;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00014"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#a0ce1d8245227b22ff06fe09915db19c5">   14</a></span>&#160;<a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="geometric__utils_8h.html#a0ce1d8245227b22ff06fe09915db19c5">eigen_value</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a>&amp; A) {</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;  Eigen::SelfAdjointEigenSolver&lt;Matf&lt;Dim, Dim&gt;&gt; es(A);</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;  <span class="keywordflow">return</span> es.eigenvalues();</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;}</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;</div><div class="line"><a name="l00020"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">   20</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#a5503e9ed3faaa114b9611829fb322981">Mat2f</a> <a class="code" href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">vec2_to_rotation</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &amp;v) {</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> yaw = std::atan2(v(1), v(0));</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;  <a class="code" href="data__type_8h.html#a5503e9ed3faaa114b9611829fb322981">Mat2f</a> R;</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;  R &lt;&lt; cos(yaw), -sin(yaw),</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    sin(yaw), cos(yaw);</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;  <span class="keywordflow">return</span> R;</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;}</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">Mat3f</a> vec3_to_rotation(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> &amp;v) {</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;  <span class="comment">// zero roll</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;  <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> rpy(0, std::atan2(-v(2), v.topRows&lt;2&gt;().norm()),</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;            std::atan2(v(1), v(0)));</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;  <a class="code" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a> qx(cos(rpy(0) / 2), sin(rpy(0) / 2), 0, 0);</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;  <a class="code" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a> qy(cos(rpy(1) / 2), 0, sin(rpy(1) / 2), 0);</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;  <a class="code" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a> qz(cos(rpy(2) / 2), 0, 0, sin(rpy(2) / 2));</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">Mat3f</a>(qz * qy * qx);</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;}</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">   39</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> <a class="code" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">sort_pts</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> &amp;pts) {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;  <span class="keywordflow">if</span>(pts.empty())</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    <span class="keywordflow">return</span> pts;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> avg = Vec2f::Zero();</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span>&amp; pt : pts)</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    avg += pt;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  avg /= pts.size();</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;std::pair&lt;decimal_t, Vec2f&gt;</a>&gt; pts_valued;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  pts_valued.resize(pts.size());</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; pts.size(); i++) {</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> theta = atan2(pts[i](1) - avg(1), pts[i](0) - avg(0));</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    pts_valued[i] = std::make_pair(theta, pts[i]);</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  }</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  std::sort(pts_valued.begin(), pts_valued.end(),</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;            [](<span class="keyword">const</span> std::pair&lt;decimal_t, Vec2f&gt; &amp;i,</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;               <span class="keyword">const</span> std::pair&lt;decimal_t, Vec2f&gt; &amp;j) {</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;              <span class="keywordflow">return</span> i.first &lt; j.first;});</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> pts_sorted(pts_valued.size());</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">size_t</span> i = 0; i &lt; pts_valued.size(); i++)</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    pts_sorted[i] = pts_valued[i].second;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keywordflow">return</span> pts_sorted;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;}</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">   69</a></span>&#160;<span class="keyword">inline</span> <span class="keywordtype">bool</span> <a class="code" href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">line_intersect</a>(<span class="keyword">const</span> std::pair&lt;Vec2f, Vec2f&gt; &amp;v1,</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                    <span class="keyword">const</span> std::pair&lt;Vec2f, Vec2f&gt; &amp;v2,</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                    <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> &amp;pi) {</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> a1 = -v1.first(1);</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> b1 = v1.first(0);</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> c1 = a1 * v1.second(0) + b1 * v1.second(1);</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> a2 = -v2.first(1);</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> b2 = v2.first(0);</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> c2 = a2 * v2.second(0) + b2 * v2.second(1);</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> x = (c1 * b2 - c2 * b1) / (a1 * b2 - a2 * b1);</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> y = (c1 * a2 - c2 * a1) / (a2 * b1 - a1 * b2);</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="keywordflow">if</span> (std::isnan(x) || std::isnan(y) || std::isinf(x) || std::isinf(y))</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keywordflow">else</span> {</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    pi &lt;&lt; x, y;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">true</span>;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  }</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;}</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">   94</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> <a class="code" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">line_intersects</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt;std::pair&lt;Vec2f, Vec2f&gt;&gt; &amp;lines) {</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> pts;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; lines.size(); i++) {</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> j = i + 1; j &lt; lines.size(); j++) {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;      <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> pi;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">line_intersect</a>(lines[i], lines[j], pi)) {</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        pts.push_back(pi);</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;      }</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    }</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  }</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <span class="keywordflow">return</span> pts;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;}</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> <a class="code" href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">cal_vertices</a>(<span class="keyword">const</span> <a class="code" href="structPolyhedron.html">Polyhedron2D</a> &amp;poly) {</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;std::pair&lt;Vec2f, Vec2f&gt;</a>&gt; lines;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="keyword">const</span> <span class="keyword">auto</span> vs = poly.<a class="code" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">hyperplanes</a>();</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; vs.size(); i++) {</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;    <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> n = vs[i].n_;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> v(-n(1), n(0));</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    v = v.normalized();</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    lines.push_back(std::make_pair(v, vs[i].p_));</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;    <span class="comment">/*</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="comment">    std::cout &lt;&lt; &quot;add p: &quot; &lt;&lt; lines.back().second.transpose() &lt;&lt;</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="comment">      &quot; v: &quot; &lt;&lt; lines.back().first.transpose() &lt;&lt; std::endl;</span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="comment">      */</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  }</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <span class="keyword">auto</span> vts = <a class="code" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">line_intersects</a>(lines);</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;  <span class="comment">//for(const auto&amp; it: vts)</span></div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="comment">//std::cout &lt;&lt; &quot;vertice: &quot; &lt;&lt; it.transpose() &lt;&lt; std::endl;</span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> vts_inside = poly.<a class="code" href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">points_inside</a>(vts);</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  vts_inside = <a class="code" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">sort_pts</a>(vts_inside);</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keywordflow">return</span> vts_inside;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;}</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">  134</a></span>&#160;<span class="keyword">inline</span> <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;vec_Vec3f&gt;</a> <a class="code" href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">cal_vertices</a>(<span class="keyword">const</span> <a class="code" href="structPolyhedron.html">Polyhedron3D</a> &amp;poly) {</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;vec_Vec3f&gt;</a> bds;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <span class="keyword">const</span> <span class="keyword">auto</span> vts = poly.<a class="code" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">hyperplanes</a>();</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="comment">//**** for each plane, find lines on it</span></div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 0; i &lt; vts.size(); i++) {</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <span class="keyword">const</span> <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> t = vts[i].p_;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <span class="keyword">const</span> <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> n = vts[i].n_;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a> q = Quatf::FromTwoVectors(<a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, 1), n);</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">Mat3f</a> R(q); <span class="comment">// body to world</span></div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E&lt;std::pair&lt;Vec2f, Vec2f&gt;</a>&gt; lines;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> j = 0; j &lt; vts.size(); j++) {</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;      <span class="keywordflow">if</span> (j == i)</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        <span class="keywordflow">continue</span>;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> nw = vts[j].n_;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;      <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> nb = R.transpose() * nw;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;      <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> bb = vts[j].p_.dot(nw) - nw.dot(t);</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> v = <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(0, 0, 1).cross(nb).topRows&lt;2&gt;(); <span class="comment">// line direction</span></div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;      <a class="code" href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a> p; <span class="comment">// point on the line</span></div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      <span class="keywordflow">if</span> (nb(1) != 0)</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        p &lt;&lt; 0, bb / nb(1);</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;      <span class="keywordflow">else</span> <span class="keywordflow">if</span> (nb(0) != 0)</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        p &lt;&lt; bb / nb(0), 0;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;      <span class="keywordflow">else</span></div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <span class="keywordflow">continue</span>;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      lines.push_back(std::make_pair(v, p));</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    }</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <span class="comment">//**** find all intersect points</span></div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> pts = <a class="code" href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">line_intersects</a>(lines);</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;    <span class="comment">//**** filter out points inside polytope</span></div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <a class="code" href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a> pts_inside;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span>&amp; it : pts) {</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;      <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> p = R * <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(it(0), it(1), 0) + t; <span class="comment">// convert to world frame</span></div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;      <span class="keywordflow">if</span> (poly.<a class="code" href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">inside</a>(p))</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        pts_inside.push_back(it);</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    }</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="keywordflow">if</span>(pts_inside.size() &gt; 2) {</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;      <span class="comment">//**** sort in plane frame</span></div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;      pts_inside = <a class="code" href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">sort_pts</a>(pts_inside);</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;      <span class="comment">//**** transform to world frame</span></div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      <a class="code" href="data__type_8h.html#a62c46ed3e3ab6773b30439f9be38290b">vec_Vec3f</a> points_valid;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;      <span class="keywordflow">for</span> (<span class="keyword">auto</span> &amp;it : pts_inside)</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;        points_valid.push_back(R * <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a>(it(0), it(1), 0) + t);</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      <span class="comment">//**** insert resulting polygon</span></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      bds.push_back(points_valid);</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    }</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  <span class="keywordflow">return</span> bds;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;}</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a231e0258efbae239a7cdfbd52442f06e"><div class="ttname"><a href="data__type_8h.html#a231e0258efbae239a7cdfbd52442f06e">Mat3f</a></div><div class="ttdeci">Matf&lt; 3, 3 &gt; Mat3f</div><div class="ttdoc">3x3 Matrix in float </div><div class="ttdef"><b>Definition:</b> data_type.h:99</div></div>
<div class="ttc" id="structPolyhedron_html_a28e5b0eebab18fb8d54fb021333363ce"><div class="ttname"><a href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">Polyhedron::inside</a></div><div class="ttdeci">bool inside(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Check if the point is inside polyhedron, non-exclusive. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:54</div></div>
<div class="ttc" id="data__utils_8h_html"><div class="ttname"><a href="data__utils_8h.html">data_utils.h</a></div><div class="ttdoc">Provide a few widely used function for basic type. </div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="geometric__utils_8h_html_aa91b5d566f48c28dff0d9aa08b4abdc2"><div class="ttname"><a href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">vec2_to_rotation</a></div><div class="ttdeci">Mat2f vec2_to_rotation(const Vec2f &amp;v)</div><div class="ttdoc">Calculate rotation matrix from a vector (aligned with x-axis) </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:20</div></div>
<div class="ttc" id="geometric__utils_8h_html_af844038aa7029551d36f807327590005"><div class="ttname"><a href="geometric__utils_8h.html#af844038aa7029551d36f807327590005">sort_pts</a></div><div class="ttdeci">vec_Vec2f sort_pts(const vec_Vec2f &amp;pts)</div><div class="ttdoc">Sort points on the same plane in the counter-clockwise order. </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:39</div></div>
<div class="ttc" id="structPolyhedron_html_ac91f6e4f56758d09dd36b139249ae566"><div class="ttname"><a href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">Polyhedron::points_inside</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; points_inside(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Calculate points inside polyhedron, non-exclusive. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:65</div></div>
<div class="ttc" id="structPolyhedron_html_a92118114a7bce0e799ff1c587fff04a4"><div class="ttname"><a href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">Polyhedron::hyperplanes</a></div><div class="ttdeci">vec_E&lt; Hyperplane&lt; Dim &gt; &gt; hyperplanes() const </div><div class="ttdoc">Get the hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:83</div></div>
<div class="ttc" id="data__type_8h_html_afd53e073786661e24985c48b4ef92fcb"><div class="ttname"><a href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a></div><div class="ttdeci">Vecf&lt; 3 &gt; Vec3f</div><div class="ttdoc">Eigen 1D float vector of size 3. </div><div class="ttdef"><b>Definition:</b> data_type.h:79</div></div>
<div class="ttc" id="data__type_8h_html_a5503e9ed3faaa114b9611829fb322981"><div class="ttname"><a href="data__type_8h.html#a5503e9ed3faaa114b9611829fb322981">Mat2f</a></div><div class="ttdeci">Matf&lt; 2, 2 &gt; Mat2f</div><div class="ttdoc">2x2 Matrix in float </div><div class="ttdef"><b>Definition:</b> data_type.h:97</div></div>
<div class="ttc" id="data__type_8h_html_a1eeda0bad4efd3be8cb2da1941982410"><div class="ttname"><a href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, M, N &gt; Matf</div><div class="ttdoc">MxN Eigen matrix. </div><div class="ttdef"><b>Definition:</b> data_type.h:63</div></div>
<div class="ttc" id="geometric__utils_8h_html_a79027aaed3d36e00b00eb58e55d95f16"><div class="ttname"><a href="geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16">line_intersect</a></div><div class="ttdeci">bool line_intersect(const std::pair&lt; Vec2f, Vec2f &gt; &amp;v1, const std::pair&lt; Vec2f, Vec2f &gt; &amp;v2, Vec2f &amp;pi)</div><div class="ttdoc">Find intersection between two lines on the same plane, return false if they are not intersected...</div><div class="ttdef"><b>Definition:</b> geometric_utils.h:69</div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="data__type_8h_html_a30c607180de5bc1b7c30f5cbaf9b188b"><div class="ttname"><a href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a></div><div class="ttdeci">std::vector&lt; T, Eigen::aligned_allocator&lt; T &gt;&gt; vec_E</div><div class="ttdoc">Pre-allocated std::vector for Eigen using vec_E. </div><div class="ttdef"><b>Definition:</b> data_type.h:54</div></div>
<div class="ttc" id="geometric__utils_8h_html_a8b4b27d3fcc8d4035d9b61a26215fd59"><div class="ttname"><a href="geometric__utils_8h.html#a8b4b27d3fcc8d4035d9b61a26215fd59">cal_vertices</a></div><div class="ttdeci">vec_E&lt; vec_Vec3f &gt; cal_vertices(const Polyhedron3D &amp;poly)</div><div class="ttdoc">Find extreme points of Polyhedron3D. </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:134</div></div>
<div class="ttc" id="data__type_8h_html_a135c596de9b80bec15985876ebd5036e"><div class="ttname"><a href="data__type_8h.html#a135c596de9b80bec15985876ebd5036e">Vec2f</a></div><div class="ttdeci">Vecf&lt; 2 &gt; Vec2f</div><div class="ttdoc">Eigen 1D float vector of size 2. </div><div class="ttdef"><b>Definition:</b> data_type.h:75</div></div>
<div class="ttc" id="data__type_8h_html_a4857a8f36ec316f647bfc006d4799e9a"><div class="ttname"><a href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a></div><div class="ttdeci">Eigen::Quaternion&lt; decimal_t &gt; Quatf</div><div class="ttdoc">Allias of Eigen::Quaterniond. </div><div class="ttdef"><b>Definition:</b> data_type.h:120</div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="geometric__utils_8h_html_a34493497e7d12d4a99e6b9b7cef007f8"><div class="ttname"><a href="geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8">line_intersects</a></div><div class="ttdeci">vec_Vec2f line_intersects(const vec_E&lt; std::pair&lt; Vec2f, Vec2f &gt;&gt; &amp;lines)</div><div class="ttdoc">Find intersection between multiple lines. </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:94</div></div>
<div class="ttc" id="data__type_8h_html_a62c46ed3e3ab6773b30439f9be38290b"><div class="ttname"><a href="data__type_8h.html#a62c46ed3e3ab6773b30439f9be38290b">vec_Vec3f</a></div><div class="ttdeci">vec_E&lt; Vec3f &gt; vec_Vec3f</div><div class="ttdoc">Vector of type Vec3f. </div><div class="ttdef"><b>Definition:</b> data_type.h:92</div></div>
<div class="ttc" id="geometric__utils_8h_html_a0ce1d8245227b22ff06fe09915db19c5"><div class="ttname"><a href="geometric__utils_8h.html#a0ce1d8245227b22ff06fe09915db19c5">eigen_value</a></div><div class="ttdeci">Vecf&lt; Dim &gt; eigen_value(const Matf&lt; Dim, Dim &gt; &amp;A)</div><div class="ttdoc">Calculate eigen values. </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:14</div></div>
<div class="ttc" id="data__type_8h_html_af640446aaa0ada84a270bd5639b36a7d"><div class="ttname"><a href="data__type_8h.html#af640446aaa0ada84a270bd5639b36a7d">vec_Vec2f</a></div><div class="ttdeci">vec_E&lt; Vec2f &gt; vec_Vec2f</div><div class="ttdoc">Vector of type Vec2f. </div><div class="ttdef"><b>Definition:</b> data_type.h:88</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
