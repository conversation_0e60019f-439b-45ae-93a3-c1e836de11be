<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Ellipsoid&lt; Dim &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structEllipsoid-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Ellipsoid&lt; Dim &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6dd2b642e3635c2646f6f1419ffebb4d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a6dd2b642e3635c2646f6f1419ffebb4d"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Ellipsoid</b> (const <a class="el" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a>&lt; Dim, Dim &gt; &amp;<a class="el" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">C</a>, const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;<a class="el" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>)</td></tr>
<tr class="separator:a6dd2b642e3635c2646f6f1419ffebb4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0d3bfad48b3bedb87dbc308d36f45a3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa0d3bfad48b3bedb87dbc308d36f45a3"></a>
<a class="el" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt) const </td></tr>
<tr class="memdesc:aa0d3bfad48b3bedb87dbc308d36f45a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate distance to the center. <br /></td></tr>
<tr class="separator:aa0d3bfad48b3bedb87dbc308d36f45a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6795795ed44656c324b68bf11111ab8c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a6795795ed44656c324b68bf11111ab8c"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">inside</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt) const </td></tr>
<tr class="memdesc:a6795795ed44656c324b68bf11111ab8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if the point is inside, non-exclusive. <br /></td></tr>
<tr class="separator:a6795795ed44656c324b68bf11111ab8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a70e2f50e23092af77fdc1766efa17c53"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a70e2f50e23092af77fdc1766efa17c53"></a>
<a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a> (const <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt; &amp;O) const </td></tr>
<tr class="memdesc:a70e2f50e23092af77fdc1766efa17c53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate points inside ellipsoid, non-exclusive. <br /></td></tr>
<tr class="separator:a70e2f50e23092af77fdc1766efa17c53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2c7ee812c96ff94a1a44ddc6a7044b8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ad2c7ee812c96ff94a1a44ddc6a7044b8"></a>
<a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a> (const <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt; &amp;O) const </td></tr>
<tr class="memdesc:ad2c7ee812c96ff94a1a44ddc6a7044b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find the closest point. <br /></td></tr>
<tr class="separator:ad2c7ee812c96ff94a1a44ddc6a7044b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dcd9a92214baad9750c8d8909e22ffc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a7dcd9a92214baad9750c8d8909e22ffc"></a>
<a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">closest_hyperplane</a> (const <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt; &amp;O) const </td></tr>
<tr class="memdesc:a7dcd9a92214baad9750c8d8909e22ffc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find the closest hyperplane from the closest point. <br /></td></tr>
<tr class="separator:a7dcd9a92214baad9750c8d8909e22ffc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e7533e61f95e20a0d503e5c665eac07"><td class="memTemplParams" colspan="2"><a class="anchor" id="a4e7533e61f95e20a0d503e5c665eac07"></a>
template&lt;int U = Dim&gt; </td></tr>
<tr class="memitem:a4e7533e61f95e20a0d503e5c665eac07"><td class="memTemplItemLeft" align="right" valign="top">std::enable_if&lt; U==2, <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; U &gt; &gt;::type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">sample</a> (int num) const </td></tr>
<tr class="memdesc:a4e7533e61f95e20a0d503e5c665eac07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sample n points along the contour. <br /></td></tr>
<tr class="separator:a4e7533e61f95e20a0d503e5c665eac07"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d61487912bc01fd8d8bc8f59b4f4ce0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a4d61487912bc01fd8d8bc8f59b4f4ce0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>print</b> () const </td></tr>
<tr class="separator:a4d61487912bc01fd8d8bc8f59b4f4ce0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c61a69a58ca6c09cc02267df6848633"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a5c61a69a58ca6c09cc02267df6848633"></a>
<a class="el" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">volume</a> () const </td></tr>
<tr class="memdesc:a5c61a69a58ca6c09cc02267df6848633"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get ellipsoid volume. <br /></td></tr>
<tr class="separator:a5c61a69a58ca6c09cc02267df6848633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a512359754637b8048a49b291ad7be957"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a512359754637b8048a49b291ad7be957"></a>
<a class="el" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a>&lt; Dim, Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">C</a> () const </td></tr>
<tr class="memdesc:a512359754637b8048a49b291ad7be957"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get C matrix. <br /></td></tr>
<tr class="separator:a512359754637b8048a49b291ad7be957"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf6b4b66f08415c042be3064d54d6e3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a2cf6b4b66f08415c042be3064d54d6e3"></a>
<a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a> () const </td></tr>
<tr class="memdesc:a2cf6b4b66f08415c042be3064d54d6e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get center. <br /></td></tr>
<tr class="separator:a2cf6b4b66f08415c042be3064d54d6e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aa5adc4235563e36a0a558ea6782f45e4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa5adc4235563e36a0a558ea6782f45e4"></a>
<a class="el" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a>&lt; Dim, Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>C_</b></td></tr>
<tr class="separator:aa5adc4235563e36a0a558ea6782f45e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a323a6e5692ef9aff9cc0eb0749c632ef"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a323a6e5692ef9aff9cc0eb0749c632ef"></a>
<a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>d_</b></td></tr>
<tr class="separator:a323a6e5692ef9aff9cc0eb0749c632ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>include/decomp_geometry/<a class="el" href="ellipsoid_8h_source.html">ellipsoid.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
