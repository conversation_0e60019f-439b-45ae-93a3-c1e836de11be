<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_geometry/ellipsoid.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_0156596343f07f423e58f27e9acfceb9.html">decomp_geometry</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">ellipsoid.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="ellipsoid_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#ifndef DECOMP_ELLIPSOID_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#define DECOMP_ELLIPSOID_H</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;</div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;iostream&gt;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="data__type_8h.html">decomp_basis/data_type.h</a>&gt;</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="preprocessor">#include &lt;decomp_geometry/polyhedron.h&gt;</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00014"></a><span class="lineno"><a class="line" href="structEllipsoid.html">   14</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structEllipsoid.html">Ellipsoid</a> {</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;  <a class="code" href="structEllipsoid.html">Ellipsoid</a>() {}</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;  <a class="code" href="structEllipsoid.html">Ellipsoid</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a>&amp; <a class="code" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">C</a>, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; <a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>) : C_(C), d_(d) {}</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00019"></a><span class="lineno"><a class="line" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">   19</a></span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> <a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt)<span class="keyword"> const </span>{</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;    <span class="keywordflow">return</span> (C_.inverse() * (pt - d_)).norm();</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;  }</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div><div class="line"><a name="l00024"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">   24</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">inside</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt)<span class="keyword"> const </span>{</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;      <span class="keywordflow">return</span> <a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(pt) &lt;= 1;</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;  }</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">   29</a></span>&#160;  <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;O)<span class="keyword"> const </span>{</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> new_O;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it : O) {</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;      <span class="keywordflow">if</span> (<a class="code" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">inside</a>(it))</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;        new_O.push_back(it);</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    }</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    <span class="keywordflow">return</span> new_O;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;  }</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">   39</a></span>&#160;  <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;O)<span class="keyword"> const </span>{</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pt = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Zero</a>();</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> min_dist = std::numeric_limits&lt;decimal_t&gt;::max();</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;    <span class="keywordflow">for</span> (<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it : O) {</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;      <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> d = <a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(it);</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;      <span class="keywordflow">if</span> (d &lt; min_dist) {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        min_dist = <a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        pt = it;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;      }</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;    }</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    <span class="keywordflow">return</span> pt;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  }</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">   53</a></span>&#160;  <a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a> <a class="code" href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">closest_hyperplane</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;O)<span class="keyword"> const </span>{</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    <span class="keyword">const</span> <span class="keyword">auto</span> closest_pt = <a class="code" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(O);</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    <span class="keyword">const</span> <span class="keyword">auto</span> n = C_.inverse() * C_.inverse().transpose() *</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;      (closest_pt - d_);</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(closest_pt, n.normalized());</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  }</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> U = Dim&gt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    <span class="keyword">typename</span> std::enable_if&lt;U == 2, vec_Vecf&lt;U&gt;&gt;::type</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">   63</a></span>&#160;    <a class="code" href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">sample</a>(<span class="keywordtype">int</span> num)<span class="keyword"> const </span>{</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> pts;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> dyaw = M_PI*2/num;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;      <span class="keywordflow">for</span>(<a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> yaw = 0; yaw &lt; M_PI*2; yaw+=dyaw) {</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pt;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        pt &lt;&lt; cos(yaw), sin(yaw);</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        pts.push_back(C_ * pt + d_);</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;    }</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <span class="keywordflow">return</span> pts;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  }</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keywordtype">void</span> print()<span class="keyword"> const </span>{</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;C: &quot;</span> &lt;&lt; C_ &lt;&lt; std::endl;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    std::cout &lt;&lt; <span class="stringliteral">&quot;d: &quot;</span> &lt;&lt; d_ &lt;&lt; std::endl;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  }</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">   80</a></span>&#160;  <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> <a class="code" href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">volume</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <span class="keywordflow">return</span> C_.determinant();</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  }</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">   85</a></span>&#160;  <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> <a class="code" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">C</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <span class="keywordflow">return</span> C_;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  }</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">   90</a></span>&#160;  <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    <span class="keywordflow">return</span> d_;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> C_;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;  <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> d_;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;};</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structEllipsoid.html">Ellipsoid&lt;2&gt;</a> <a class="code" href="structEllipsoid.html">Ellipsoid2D</a>;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="keyword">typedef</span> <a class="code" href="structEllipsoid.html">Ellipsoid&lt;3&gt;</a> <a class="code" href="structEllipsoid.html">Ellipsoid3D</a>;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a74599b2a677a5186ef71a2a690c6171d"><div class="ttname"><a href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a></div><div class="ttdeci">vec_E&lt; Vecf&lt; N &gt;&gt; vec_Vecf</div><div class="ttdoc">Vector of Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:69</div></div>
<div class="ttc" id="structEllipsoid_html_a6795795ed44656c324b68bf11111ab8c"><div class="ttname"><a href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">Ellipsoid::inside</a></div><div class="ttdeci">bool inside(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Check if the point is inside, non-exclusive. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:24</div></div>
<div class="ttc" id="structEllipsoid_html_aa0d3bfad48b3bedb87dbc308d36f45a3"><div class="ttname"><a href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">Ellipsoid::dist</a></div><div class="ttdeci">decimal_t dist(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Calculate distance to the center. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:19</div></div>
<div class="ttc" id="structEllipsoid_html"><div class="ttname"><a href="structEllipsoid.html">Ellipsoid</a></div><div class="ttdef"><b>Definition:</b> ellipsoid.h:14</div></div>
<div class="ttc" id="structEllipsoid_html_a70e2f50e23092af77fdc1766efa17c53"><div class="ttname"><a href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">Ellipsoid::points_inside</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; points_inside(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Calculate points inside ellipsoid, non-exclusive. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:29</div></div>
<div class="ttc" id="structEllipsoid_html_a7dcd9a92214baad9750c8d8909e22ffc"><div class="ttname"><a href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">Ellipsoid::closest_hyperplane</a></div><div class="ttdeci">Hyperplane&lt; Dim &gt; closest_hyperplane(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Find the closest hyperplane from the closest point. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:53</div></div>
<div class="ttc" id="structEllipsoid_html_ad2c7ee812c96ff94a1a44ddc6a7044b8"><div class="ttname"><a href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">Ellipsoid::closest_point</a></div><div class="ttdeci">Vecf&lt; Dim &gt; closest_point(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Find the closest point. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:39</div></div>
<div class="ttc" id="data__type_8h_html_a1eeda0bad4efd3be8cb2da1941982410"><div class="ttname"><a href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, M, N &gt; Matf</div><div class="ttdoc">MxN Eigen matrix. </div><div class="ttdef"><b>Definition:</b> data_type.h:63</div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="structEllipsoid_html_a4e7533e61f95e20a0d503e5c665eac07"><div class="ttname"><a href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">Ellipsoid::sample</a></div><div class="ttdeci">std::enable_if&lt; U==2, vec_Vecf&lt; U &gt; &gt;::type sample(int num) const </div><div class="ttdoc">Sample n points along the contour. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:63</div></div>
<div class="ttc" id="data__type_8h_html"><div class="ttname"><a href="data__type_8h.html">data_type.h</a></div><div class="ttdoc">Defines all data types used in this lib. </div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="structHyperplane_html"><div class="ttname"><a href="structHyperplane.html">Hyperplane</a></div><div class="ttdoc">Hyperplane class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:13</div></div>
<div class="ttc" id="structEllipsoid_html_a2cf6b4b66f08415c042be3064d54d6e3"><div class="ttname"><a href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">Ellipsoid::d</a></div><div class="ttdeci">Vecf&lt; Dim &gt; d() const </div><div class="ttdoc">Get center. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:90</div></div>
<div class="ttc" id="structEllipsoid_html_a512359754637b8048a49b291ad7be957"><div class="ttname"><a href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">Ellipsoid::C</a></div><div class="ttdeci">Matf&lt; Dim, Dim &gt; C() const </div><div class="ttdoc">Get C matrix. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:85</div></div>
<div class="ttc" id="structEllipsoid_html_a5c61a69a58ca6c09cc02267df6848633"><div class="ttname"><a href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">Ellipsoid::volume</a></div><div class="ttdeci">decimal_t volume() const </div><div class="ttdoc">Get ellipsoid volume. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:80</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
