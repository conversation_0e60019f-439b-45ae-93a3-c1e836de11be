<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Polyhedron&lt; Dim &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structPolyhedron-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Polyhedron&lt; Dim &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="structPolyhedron.html" title="Polyhedron class. ">Polyhedron</a> class.  
 <a href="structPolyhedron.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8b9a5d2d44059c016486be3e3e05ddeb"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a8b9a5d2d44059c016486be3e3e05ddeb"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#a8b9a5d2d44059c016486be3e3e05ddeb">Polyhedron</a> ()</td></tr>
<tr class="memdesc:a8b9a5d2d44059c016486be3e3e05ddeb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Null constructor. <br /></td></tr>
<tr class="separator:a8b9a5d2d44059c016486be3e3e05ddeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a54b2d009a7392830934f04f5226fc591"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a54b2d009a7392830934f04f5226fc591"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#a54b2d009a7392830934f04f5226fc591">Polyhedron</a> (const <a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt;&gt; &amp;vs)</td></tr>
<tr class="memdesc:a54b2d009a7392830934f04f5226fc591"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construct from <a class="el" href="structHyperplane.html" title="Hyperplane class. ">Hyperplane</a> array. <br /></td></tr>
<tr class="separator:a54b2d009a7392830934f04f5226fc591"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1efa5c7b822945d37c93a38667f8d04d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a1efa5c7b822945d37c93a38667f8d04d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a> (const <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt; &amp;v)</td></tr>
<tr class="memdesc:a1efa5c7b822945d37c93a38667f8d04d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Append <a class="el" href="structHyperplane.html" title="Hyperplane class. ">Hyperplane</a>. <br /></td></tr>
<tr class="separator:a1efa5c7b822945d37c93a38667f8d04d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28e5b0eebab18fb8d54fb021333363ce"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a28e5b0eebab18fb8d54fb021333363ce"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#a28e5b0eebab18fb8d54fb021333363ce">inside</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt) const </td></tr>
<tr class="memdesc:a28e5b0eebab18fb8d54fb021333363ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if the point is inside polyhedron, non-exclusive. <br /></td></tr>
<tr class="separator:a28e5b0eebab18fb8d54fb021333363ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac91f6e4f56758d09dd36b139249ae566"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ac91f6e4f56758d09dd36b139249ae566"></a>
<a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#ac91f6e4f56758d09dd36b139249ae566">points_inside</a> (const <a class="el" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a>&lt; Dim &gt; &amp;O) const </td></tr>
<tr class="memdesc:ac91f6e4f56758d09dd36b139249ae566"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate points inside polyhedron, non-exclusive. <br /></td></tr>
<tr class="separator:ac91f6e4f56758d09dd36b139249ae566"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf89b0bd80d59b3ff4c7f40b3872e2f3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aaf89b0bd80d59b3ff4c7f40b3872e2f3"></a>
<a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; std::pair&lt; <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;, <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#aaf89b0bd80d59b3ff4c7f40b3872e2f3">cal_normals</a> () const </td></tr>
<tr class="memdesc:aaf89b0bd80d59b3ff4c7f40b3872e2f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate normals, used for visualization. <br /></td></tr>
<tr class="separator:aaf89b0bd80d59b3ff4c7f40b3872e2f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92118114a7bce0e799ff1c587fff04a4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a92118114a7bce0e799ff1c587fff04a4"></a>
<a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">hyperplanes</a> () const </td></tr>
<tr class="memdesc:a92118114a7bce0e799ff1c587fff04a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the hyperplane array. <br /></td></tr>
<tr class="separator:a92118114a7bce0e799ff1c587fff04a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ad65ac3518e69a9f99ba41d23f943034b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ad65ac3518e69a9f99ba41d23f943034b"></a>
<a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt; &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structPolyhedron.html#ad65ac3518e69a9f99ba41d23f943034b">vs_</a></td></tr>
<tr class="memdesc:ad65ac3518e69a9f99ba41d23f943034b"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="structHyperplane.html" title="Hyperplane class. ">Hyperplane</a> array. <br /></td></tr>
<tr class="separator:ad65ac3518e69a9f99ba41d23f943034b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int Dim&gt;<br />
struct Polyhedron&lt; Dim &gt;</h3>

<p><a class="el" href="structPolyhedron.html" title="Polyhedron class. ">Polyhedron</a> class. </p>
</div><hr/>The documentation for this struct was generated from the following file:<ul>
<li>include/decomp_geometry/<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
