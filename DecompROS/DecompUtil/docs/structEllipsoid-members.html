<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Ellipsoid&lt; Dim &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#a512359754637b8048a49b291ad7be957">C</a>() const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>C_</b> (defined in <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>)</td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#a7dcd9a92214baad9750c8d8909e22ffc">closest_hyperplane</a>(const vec_Vecf&lt; Dim &gt; &amp;O) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(const vec_Vecf&lt; Dim &gt; &amp;O) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>() const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>d_</b> (defined in <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>)</td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(const Vecf&lt; Dim &gt; &amp;pt) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>Ellipsoid</b>() (defined in <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>)</td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>Ellipsoid</b>(const Matf&lt; Dim, Dim &gt; &amp;C, const Vecf&lt; Dim &gt; &amp;d) (defined in <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>)</td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structEllipsoid.html#a6795795ed44656c324b68bf11111ab8c">inside</a>(const Vecf&lt; Dim &gt; &amp;pt) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a>(const vec_Vecf&lt; Dim &gt; &amp;O) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>print</b>() const  (defined in <a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a>)</td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="structEllipsoid.html#a4e7533e61f95e20a0d503e5c665eac07">sample</a>(int num) const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="structEllipsoid.html#a5c61a69a58ca6c09cc02267df6848633">volume</a>() const </td><td class="entry"><a class="el" href="structEllipsoid.html">Ellipsoid&lt; Dim &gt;</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
