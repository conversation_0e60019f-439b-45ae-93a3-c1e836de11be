<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_util/iterative_decomp.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_75993fee8576b97e3d9a8476c1772d17.html">decomp_util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">iterative_decomp.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="iterative__decomp_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#ifndef ITERATIVE_DECOMP_H</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define ITERATIVE_DECOMP_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="ellipsoid__decomp_8h.html">decomp_util/ellipsoid_decomp.h</a>&gt;</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html">   16</a></span>&#160;<span class="keyword">class </span><a class="code" href="classIterativeDecomp.html">IterativeDecomp</a> : <span class="keyword">public</span> <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp</a>&lt;Dim&gt;</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;{</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00020"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#af2649785ec5548cdf0f59e96486a2181">   20</a></span>&#160;    <a class="code" href="classIterativeDecomp.html#af2649785ec5548cdf0f59e96486a2181">IterativeDecomp</a>() {}</div><div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#a7438eb4550b447e1e5422bb99359a39d">   26</a></span>&#160;    <a class="code" href="classIterativeDecomp.html#a7438eb4550b447e1e5422bb99359a39d">IterativeDecomp</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;origin, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;dim) :</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;        <a class="code" href="classEllipsoidDecomp.html">EllipsoidDecomp</a>&lt;Dim&gt;(origin, dim) {}</div><div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#ad735b75d676b2f4ee3b8f2015f95559c">   35</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classIterativeDecomp.html#ad735b75d676b2f4ee3b8f2015f95559c">dilate_iter</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;path_raw, <span class="keywordtype">int</span> iter_num = 5,</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> res = 0, <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> offset_x = 0) {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;      <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> path = res &gt; 0 ? <a class="code" href="classIterativeDecomp.html#a359ab6e36450d30ea16a6ad885f27648">downsample</a>(path_raw, res) : path_raw;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;      this-&gt;<a class="code" href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">dilate</a>(path, offset_x);</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;      <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> new_path = <a class="code" href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">simplify</a>(path);</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; iter_num; i++) {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        <span class="keywordflow">if</span> (new_path.size() == path.size())</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;          <span class="keywordflow">break</span>;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        <span class="keywordflow">else</span> {</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;          path = new_path;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;          this-&gt;<a class="code" href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">dilate</a>(path, offset_x);</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;          new_path = <a class="code" href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">simplify</a>(path);</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        }</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;      }</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;    }</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">protected</span>:</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#a359ab6e36450d30ea16a6ad885f27648">   53</a></span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="classIterativeDecomp.html#a359ab6e36450d30ea16a6ad885f27648">downsample</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> &amp;ps, <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> d) {</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;      <span class="comment">// subdivide according to length</span></div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;      <span class="keywordflow">if</span> (ps.size() &lt; 2)</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keywordflow">return</span> ps;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;      <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> path;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> i = 1; i &lt; ps.size(); i++) {</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> dist = (ps[i] - ps[i - 1]).norm();</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;        <span class="keywordtype">int</span> cnt = std::ceil(dist / d);</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; cnt; j++)</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;          path.push_back(ps[i - 1] + j * (ps[i] - ps[i - 1]) / cnt);</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      }</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      path.push_back(ps.back());</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      <span class="keywordflow">return</span> path;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;    }</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#a6df4c75ba768e0b0866ab0de79955639">   69</a></span>&#160;    <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> <a class="code" href="classIterativeDecomp.html#a6df4c75ba768e0b0866ab0de79955639">cal_closest_dist</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a>&amp; pt, <span class="keyword">const</span> <a class="code" href="structPolyhedron.html">Polyhedron&lt;Dim&gt;</a>&amp; vs){</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> dist = std::numeric_limits&lt;decimal_t&gt;::infinity();</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;      <span class="keywordflow">for</span>(<span class="keyword">const</span> <span class="keyword">auto</span>&amp; it: vs.<a class="code" href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">hyperplanes</a>()){</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> d = std::abs(it.n_.dot(pt - it.p_));</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;        <span class="keywordflow">if</span>(d &lt; dist)</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;          dist = d;</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      }</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;      <span class="keywordflow">return</span> dist;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    }</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">   80</a></span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">simplify</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a>&amp; path) {</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;            <span class="keywordflow">if</span>(path.size() &lt;= 2)</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                <span class="keywordflow">return</span> path;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;            <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> ref_pt = path.front();</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;            <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> new_path;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;            new_path.push_back(ref_pt);</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;            <span class="keywordflow">for</span>(<span class="keywordtype">size_t</span> i = 2; i &lt; path.size(); i ++){</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                <span class="keywordflow">if</span>(this-&gt;polyhedrons_[i-1].inside(ref_pt) &amp;&amp;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                     <a class="code" href="classIterativeDecomp.html#a6df4c75ba768e0b0866ab0de79955639">cal_closest_dist</a>(ref_pt, this-&gt;polyhedrons_[i-1]) &gt; 0.1) {</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                <span class="keywordflow">else</span>{</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                    ref_pt = path[i-1];</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                    new_path.push_back(ref_pt);</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                }</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;            }</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;            new_path.push_back(path.back());</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;            <span class="keywordflow">return</span> new_path;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        }</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;};</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classIterativeDecomp.html">IterativeDecomp&lt;2&gt;</a> <a class="code" href="classIterativeDecomp.html">IterativeDecomp2D</a>;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classIterativeDecomp.html">IterativeDecomp&lt;3&gt;</a> <a class="code" href="classIterativeDecomp.html">IterativeDecomp3D</a>;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a74599b2a677a5186ef71a2a690c6171d"><div class="ttname"><a href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a></div><div class="ttdeci">vec_E&lt; Vecf&lt; N &gt;&gt; vec_Vecf</div><div class="ttdoc">Vector of Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:69</div></div>
<div class="ttc" id="classEllipsoidDecomp_html"><div class="ttname"><a href="classEllipsoidDecomp.html">EllipsoidDecomp</a></div><div class="ttdoc">EllipsoidDecomp Class. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:17</div></div>
<div class="ttc" id="classEllipsoidDecomp_html_aaf4731df44249fe6ed026efa12d6bf63"><div class="ttname"><a href="classEllipsoidDecomp.html#aaf4731df44249fe6ed026efa12d6bf63">EllipsoidDecomp::dilate</a></div><div class="ttdeci">void dilate(const vec_Vecf&lt; Dim &gt; &amp;path, double offset_x=0)</div><div class="ttdoc">Decomposition thread. </div><div class="ttdef"><b>Definition:</b> ellipsoid_decomp.h:62</div></div>
<div class="ttc" id="classIterativeDecomp_html_a6df4c75ba768e0b0866ab0de79955639"><div class="ttname"><a href="classIterativeDecomp.html#a6df4c75ba768e0b0866ab0de79955639">IterativeDecomp::cal_closest_dist</a></div><div class="ttdeci">decimal_t cal_closest_dist(const Vecf&lt; Dim &gt; &amp;pt, const Polyhedron&lt; Dim &gt; &amp;vs)</div><div class="ttdoc">Get closest distance. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:69</div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="ellipsoid__decomp_8h_html"><div class="ttname"><a href="ellipsoid__decomp_8h.html">ellipsoid_decomp.h</a></div><div class="ttdoc">EllipsoidDecomp Class. </div></div>
<div class="ttc" id="structPolyhedron_html_a92118114a7bce0e799ff1c587fff04a4"><div class="ttname"><a href="structPolyhedron.html#a92118114a7bce0e799ff1c587fff04a4">Polyhedron::hyperplanes</a></div><div class="ttdeci">vec_E&lt; Hyperplane&lt; Dim &gt; &gt; hyperplanes() const </div><div class="ttdoc">Get the hyperplane array. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:83</div></div>
<div class="ttc" id="classIterativeDecomp_html_a7438eb4550b447e1e5422bb99359a39d"><div class="ttname"><a href="classIterativeDecomp.html#a7438eb4550b447e1e5422bb99359a39d">IterativeDecomp::IterativeDecomp</a></div><div class="ttdeci">IterativeDecomp(const Vecf&lt; Dim &gt; &amp;origin, const Vecf&lt; Dim &gt; &amp;dim)</div><div class="ttdoc">Basic constructor. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:26</div></div>
<div class="ttc" id="classIterativeDecomp_html"><div class="ttname"><a href="classIterativeDecomp.html">IterativeDecomp</a></div><div class="ttdoc">IterativeDecomp Class. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:16</div></div>
<div class="ttc" id="classIterativeDecomp_html_af2649785ec5548cdf0f59e96486a2181"><div class="ttname"><a href="classIterativeDecomp.html#af2649785ec5548cdf0f59e96486a2181">IterativeDecomp::IterativeDecomp</a></div><div class="ttdeci">IterativeDecomp()</div><div class="ttdoc">Simple constructor. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:20</div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="classIterativeDecomp_html_a359ab6e36450d30ea16a6ad885f27648"><div class="ttname"><a href="classIterativeDecomp.html#a359ab6e36450d30ea16a6ad885f27648">IterativeDecomp::downsample</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; downsample(const vec_Vecf&lt; Dim &gt; &amp;ps, decimal_t d)</div><div class="ttdoc">Uniformly sample path into many segments. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:53</div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="classIterativeDecomp_html_a26f2e63081817fd284c38181bd09a3d8"><div class="ttname"><a href="classIterativeDecomp.html#a26f2e63081817fd284c38181bd09a3d8">IterativeDecomp::simplify</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; simplify(const vec_Vecf&lt; Dim &gt; &amp;path)</div><div class="ttdoc">Remove redundant waypoints. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:80</div></div>
<div class="ttc" id="classIterativeDecomp_html_ad735b75d676b2f4ee3b8f2015f95559c"><div class="ttname"><a href="classIterativeDecomp.html#ad735b75d676b2f4ee3b8f2015f95559c">IterativeDecomp::dilate_iter</a></div><div class="ttdeci">void dilate_iter(const vec_Vecf&lt; Dim &gt; &amp;path_raw, int iter_num=5, decimal_t res=0, decimal_t offset_x=0)</div><div class="ttdoc">Decomposition thread. </div><div class="ttdef"><b>Definition:</b> iterative_decomp.h:35</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
