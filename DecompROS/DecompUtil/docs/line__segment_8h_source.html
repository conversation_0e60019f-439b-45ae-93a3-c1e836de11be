<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: include/decomp_util/line_segment.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_75993fee8576b97e3d9a8476c1772d17.html">decomp_util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">line_segment.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="line__segment_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#ifndef LINE_SEGMENT_H</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define LINE_SEGMENT_H</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="decomp__base_8h.html">decomp_util/decomp_base.h</a>&gt;</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;<a class="code" href="geometric__utils_8h.html">decomp_geometry/geometric_utils.h</a>&gt;</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Dim&gt;</div><div class="line"><a name="l00017"></a><span class="lineno"><a class="line" href="classLineSegment.html">   17</a></span>&#160;<span class="keyword">class </span><a class="code" href="classLineSegment.html">LineSegment</a> : <span class="keyword">public</span> <a class="code" href="classDecompBase.html">DecompBase</a>&lt;Dim&gt; {</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;  <span class="keyword">public</span>:</div><div class="line"><a name="l00020"></a><span class="lineno"><a class="line" href="classLineSegment.html#a88f0a13c753958dcfc0040d412dcd92a">   20</a></span>&#160;    <a class="code" href="classLineSegment.html#a88f0a13c753958dcfc0040d412dcd92a">LineSegment</a>() {};</div><div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="classLineSegment.html#a20e5627453b54a1642bac236b41bfb60">   26</a></span>&#160;    <a class="code" href="classLineSegment.html#a20e5627453b54a1642bac236b41bfb60">LineSegment</a>(<span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;p1, <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> &amp;p2) : <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>(p1), <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>(p2) {}</div><div class="line"><a name="l00031"></a><span class="lineno"><a class="line" href="classLineSegment.html#a3479e2467a33468749971e12aa15c46f">   31</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classLineSegment.html#a3479e2467a33468749971e12aa15c46f">dilate</a>(<a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> radius) {</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;      <a class="code" href="classLineSegment.html#a0f2e7b18d162be0481f4e5be1cea5363">find_ellipsoid</a>(radius);</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;      this-&gt;find_polyhedron();</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;      <a class="code" href="classLineSegment.html#ac572aa41bea2d3c706d3dd6a1bb24e57">add_local_bbox</a>(this-&gt;<a class="code" href="classDecompBase.html#a21a9039cefecfe2166dc5faf8ba86a68">polyhedron_</a>);</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    }</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="classLineSegment.html#ac2de795a258c1ee25544188033ecc269">   38</a></span>&#160;    <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> <a class="code" href="classLineSegment.html#ac2de795a258c1ee25544188033ecc269">get_line_segment</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;      <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> line;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;      line.push_back(<a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>);</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;      line.push_back(<a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>);</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;      <span class="keywordflow">return</span> line;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    }</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;  <span class="keyword">protected</span>:</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="classLineSegment.html#ac572aa41bea2d3c706d3dd6a1bb24e57">   47</a></span>&#160;    <span class="keywordtype">void</span> <a class="code" href="classLineSegment.html#ac572aa41bea2d3c706d3dd6a1bb24e57">add_local_bbox</a>(<a class="code" href="structPolyhedron.html">Polyhedron&lt;Dim&gt;</a> &amp;Vs) {</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;      <span class="keywordflow">if</span>(this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>.norm() == 0)</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        <span class="keywordflow">return</span>;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;      <span class="comment">//**** virtual walls parallel to path p1-&gt;p2</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir = (<a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a> - <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>).normalized();</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir_h = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Zero</a>();</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;      dir_h(0) = dir(1), dir_h(1) = -dir(0);</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;      <span class="keywordflow">if</span> (dir_h.norm() == 0) {</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="keywordflow">if</span>(Dim == 2)</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;          dir_h &lt;&lt; -1, 0;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keywordflow">else</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;          dir_h &lt;&lt; -1, 0, 0;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      }</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;      dir_h = dir_h.normalized();</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      <span class="comment">// along x</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp1 = <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> + dir_h * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(1);</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp2 = <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> - dir_h * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(1);</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp1, dir_h));</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp2, -dir_h));</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;      <span class="comment">// along y</span></div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp3 = <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a> + dir * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(0);</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp4 = <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> - dir * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(0);</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp3, dir));</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;      Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp4, -dir));</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;      <span class="comment">// along z</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      <span class="keywordflow">if</span>(Dim &gt; 2) {</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> dir_v;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        dir_v(0) = dir(1) * dir_h(2) - dir(2) * dir_h(1);</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        dir_v(1) = dir(2) * dir_h(0) - dir(0) * dir_h(2);</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        dir_v(2) = dir(0) * dir_h(1) - dir(1) * dir_h(0);</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp5 = <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> + dir_v * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(2);</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> pp6 = <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> - dir_v * this-&gt;<a class="code" href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">local_bbox_</a>(2);</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;        Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp5, dir_v));</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        Vs.<a class="code" href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">add</a>(<a class="code" href="structHyperplane.html">Hyperplane&lt;Dim&gt;</a>(pp6, -dir_v));</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;      }</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    }</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> U = Dim&gt;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      <span class="keyword">typename</span> std::enable_if&lt;U == 2&gt;::type</div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classLineSegment.html#a0f2e7b18d162be0481f4e5be1cea5363">   90</a></span>&#160;      <a class="code" href="classLineSegment.html#a0f2e7b18d162be0481f4e5be1cea5363">find_ellipsoid</a>(<span class="keywordtype">double</span> offset_x) {</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;        <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> f = (<a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> - <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>).norm() / 2;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;        <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> C = f * <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;::Identity</a>();</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> axes = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Constant</a>(f);</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        C(0, 0) += offset_x;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        axes(0) += offset_x;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <span class="keywordflow">if</span>(axes(0) &gt; 0) {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;          <span class="keywordtype">double</span> ratio = axes(1) / axes(0);</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;          axes *= ratio;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;          C *= ratio;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        }</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        <span class="keyword">const</span> <span class="keyword">auto</span> Ri = <a class="code" href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">vec2_to_rotation</a>(<a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a> - <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>);</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        C = Ri * C * Ri.transpose();</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <a class="code" href="structEllipsoid.html">Ellipsoid&lt;Dim&gt;</a> E(C, (<a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> + <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>) / 2);</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">auto</span> obs = E.<a class="code" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a>(this-&gt;<a class="code" href="classDecompBase.html#af3afcc5ba0baa24fa1a52a08138fb67b">obs_</a>);</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <span class="keyword">auto</span> obs_inside = obs;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        <span class="comment">//**** decide short axes</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        <span class="keywordflow">while</span> (!obs_inside.empty()) {</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;          <span class="keyword">const</span> <span class="keyword">auto</span> pw = E.<a class="code" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(obs_inside);</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;          <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> p = Ri.transpose() * (pw - E.<a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>()); <span class="comment">// to ellipsoid frame</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;          <span class="keywordflow">if</span>(p(0) &lt; axes(0))</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;            axes(1) = std::abs(p(1)) / std::sqrt(1 - std::pow(p(0) / axes(0), 2));</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;          <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> new_C = Matf&lt;Dim, Dim&gt;::Identity();</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;          new_C(0, 0) = axes(0);</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;          new_C(1, 1) = axes(1);</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;          E.C_ = Ri * new_C * Ri.transpose();</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;          <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> obs_new;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;          <span class="keywordflow">for</span>(<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it: obs_inside) {</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;            <span class="keywordflow">if</span>(1 - E.<a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(it) &gt; <a class="code" href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a>)</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;              obs_new.push_back(it);</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;          }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;          obs_inside = obs_new;</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        }</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        this-&gt;<a class="code" href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">ellipsoid_</a> = E;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;      }</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    <span class="keyword">template</span>&lt;<span class="keywordtype">int</span> U = Dim&gt;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;      <span class="keyword">typename</span> std::enable_if&lt;U == 3&gt;::type</div><div class="line"><a name="l00136"></a><span class="lineno"><a class="line" href="classLineSegment.html#a9f97fdceff2bb935c290fb7767e3a696">  136</a></span>&#160;      <a class="code" href="classLineSegment.html#a9f97fdceff2bb935c290fb7767e3a696">find_ellipsoid</a>(<span class="keywordtype">double</span> offset_x) {</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;      <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> f = (<a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> - <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>).norm() / 2;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;      <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> C = f * <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;::Identity</a>();</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;      <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> axes = <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;::Constant</a>(f);</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;      C(0, 0) += offset_x;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;      axes(0) += offset_x;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;      <span class="keywordflow">if</span>(axes(0) &gt; 0) {</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="keywordtype">double</span> ratio = axes(1) / axes(0);</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        axes *= ratio;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        C *= ratio;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      }</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;      <span class="keyword">const</span> <span class="keyword">auto</span> Ri = vec3_to_rotation(<a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a> - <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>);</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      C = Ri * C * Ri.transpose();</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      <a class="code" href="structEllipsoid.html">Ellipsoid&lt;Dim&gt;</a> E(C, (<a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a> + <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>) / 2);</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;      <span class="keyword">auto</span> Rf = Ri;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;      <span class="keyword">auto</span> obs = E.<a class="code" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a>(this-&gt;<a class="code" href="classDecompBase.html#af3afcc5ba0baa24fa1a52a08138fb67b">obs_</a>);</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;      <span class="keyword">auto</span> obs_inside = obs;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;      <span class="comment">//**** decide short axes</span></div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      <span class="keywordflow">while</span> (!obs_inside.empty()) {</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keyword">const</span> <span class="keyword">auto</span> pw = E.<a class="code" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(obs_inside);</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> p = Ri.transpose() * (pw - E.<a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>()); <span class="comment">// to ellipsoid frame</span></div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        <span class="keyword">const</span> <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> roll = atan2(p(2), p(1));</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        Rf = Ri * <a class="code" href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a>(cos(roll / 2), sin(roll / 2), 0, 0);</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        p = Rf.transpose() * (pw - E.<a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>());</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        <span class="keywordflow">if</span>(p(0) &lt; axes(0))</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;          axes(1) = std::abs(p(1)) / std::sqrt(1 - std::pow(p(0) / axes(0), 2));</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;        <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> new_C = Matf&lt;Dim, Dim&gt;::Identity();</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        new_C(0, 0) = axes(0);</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        new_C(1, 1) = axes(1);</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        new_C(2, 2) = axes(1);</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;        E.C_ = Rf * new_C * Rf.transpose();</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;        <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> obs_new;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <span class="keywordflow">for</span>(<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it: obs_inside) {</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;          <span class="keywordflow">if</span>(1 - E.<a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(it) &gt; <a class="code" href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a>)</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;            obs_new.push_back(it);</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;        }</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;        obs_inside = obs_new;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;      }</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      <span class="comment">//**** reset ellipsoid with old axes(2)</span></div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;      C = f * Matf&lt;Dim, Dim&gt;::Identity();</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;      C(0, 0) = axes(0);</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      C(1, 1) = axes(1);</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;      C(2, 2) = axes(2);</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;      E.C_ = Rf * C * Rf.transpose();</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      obs_inside = E.<a class="code" href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">points_inside</a>(obs);</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;      <span class="keywordflow">while</span> (!obs_inside.empty()) {</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        <span class="keyword">const</span> <span class="keyword">auto</span> pw = E.<a class="code" href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">closest_point</a>(obs_inside);</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <a class="code" href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a> p = Rf.transpose() * (pw - E.<a class="code" href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">d</a>());</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;        <a class="code" href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a> dd = 1 - std::pow(p(0) / axes(0), 2) -</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;          std::pow(p(1) / axes(1), 2);</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        <span class="keywordflow">if</span>(dd &gt; 0)</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        axes(2) = std::abs(p(2)) / std::sqrt(dd);</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;        <a class="code" href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf&lt;Dim, Dim&gt;</a> new_C = Matf&lt;Dim, Dim&gt;::Identity();</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;        new_C(0, 0) = axes(0);</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        new_C(1, 1) = axes(1);</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;        new_C(2, 2) = axes(2);</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;        E.C_ = Rf * new_C * Rf.transpose();</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;        <a class="code" href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf&lt;Dim&gt;</a> obs_new;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        <span class="keywordflow">for</span>(<span class="keyword">const</span> <span class="keyword">auto</span> &amp;it: obs_inside) {</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;          <span class="keywordflow">if</span>(1 - E.<a class="code" href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">dist</a>(it) &gt; <a class="code" href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a>)</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;            obs_new.push_back(it);</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;        }</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        obs_inside = obs_new;</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      }</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;      this-&gt;<a class="code" href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">ellipsoid_</a> = E;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    }</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">  215</a></span>&#160;    <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">p1_</a>;</div><div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">  217</a></span>&#160;    <a class="code" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf&lt;Dim&gt;</a> <a class="code" href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">p2_</a>;</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;};</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classLineSegment.html">LineSegment&lt;2&gt;</a> <a class="code" href="classLineSegment.html">LineSegment2D</a>;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="keyword">typedef</span> <a class="code" href="classLineSegment.html">LineSegment&lt;3&gt;</a> <a class="code" href="classLineSegment.html">LineSegment3D</a>;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;<span class="preprocessor">#endif</span></div><div class="ttc" id="data__type_8h_html_a74599b2a677a5186ef71a2a690c6171d"><div class="ttname"><a href="data__type_8h.html#a74599b2a677a5186ef71a2a690c6171d">vec_Vecf</a></div><div class="ttdeci">vec_E&lt; Vecf&lt; N &gt;&gt; vec_Vecf</div><div class="ttdoc">Vector of Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:69</div></div>
<div class="ttc" id="classDecompBase_html"><div class="ttname"><a href="classDecompBase.html">DecompBase</a></div><div class="ttdoc">Line Segment Class. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:18</div></div>
<div class="ttc" id="classDecompBase_html_a8af5e7a5c407102df57089253b35c44a"><div class="ttname"><a href="classDecompBase.html#a8af5e7a5c407102df57089253b35c44a">DecompBase::local_bbox_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; local_bbox_</div><div class="ttdoc">Local bounding box along the line segment. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:94</div></div>
<div class="ttc" id="classLineSegment_html_a7fb44056def6884bdb046629c25818b6"><div class="ttname"><a href="classLineSegment.html#a7fb44056def6884bdb046629c25818b6">LineSegment::p2_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; p2_</div><div class="ttdoc">The other end of line segment, input. </div><div class="ttdef"><b>Definition:</b> line_segment.h:217</div></div>
<div class="ttc" id="classLineSegment_html"><div class="ttname"><a href="classLineSegment.html">LineSegment</a></div><div class="ttdoc">Line Segment Class. </div><div class="ttdef"><b>Definition:</b> line_segment.h:17</div></div>
<div class="ttc" id="classDecompBase_html_a21a9039cefecfe2166dc5faf8ba86a68"><div class="ttname"><a href="classDecompBase.html#a21a9039cefecfe2166dc5faf8ba86a68">DecompBase::polyhedron_</a></div><div class="ttdeci">Polyhedron&lt; Dim &gt; polyhedron_</div><div class="ttdoc">Output polyhedron. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:91</div></div>
<div class="ttc" id="structPolyhedron_html"><div class="ttname"><a href="structPolyhedron.html">Polyhedron</a></div><div class="ttdoc">Polyhedron class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:41</div></div>
<div class="ttc" id="geometric__utils_8h_html_aa91b5d566f48c28dff0d9aa08b4abdc2"><div class="ttname"><a href="geometric__utils_8h.html#aa91b5d566f48c28dff0d9aa08b4abdc2">vec2_to_rotation</a></div><div class="ttdeci">Mat2f vec2_to_rotation(const Vec2f &amp;v)</div><div class="ttdoc">Calculate rotation matrix from a vector (aligned with x-axis) </div><div class="ttdef"><b>Definition:</b> geometric_utils.h:20</div></div>
<div class="ttc" id="data__type_8h_html_a81ebeac2c4a6e9be147beb487779e9b5"><div class="ttname"><a href="data__type_8h.html#a81ebeac2c4a6e9be147beb487779e9b5">epsilon_</a></div><div class="ttdeci">constexpr decimal_t epsilon_</div><div class="ttdoc">Compensate for numerical error. </div><div class="ttdef"><b>Definition:</b> data_type.h:126</div></div>
<div class="ttc" id="structEllipsoid_html_aa0d3bfad48b3bedb87dbc308d36f45a3"><div class="ttname"><a href="structEllipsoid.html#aa0d3bfad48b3bedb87dbc308d36f45a3">Ellipsoid::dist</a></div><div class="ttdeci">decimal_t dist(const Vecf&lt; Dim &gt; &amp;pt) const </div><div class="ttdoc">Calculate distance to the center. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:19</div></div>
<div class="ttc" id="structEllipsoid_html"><div class="ttname"><a href="structEllipsoid.html">Ellipsoid</a></div><div class="ttdef"><b>Definition:</b> ellipsoid.h:14</div></div>
<div class="ttc" id="structPolyhedron_html_a1efa5c7b822945d37c93a38667f8d04d"><div class="ttname"><a href="structPolyhedron.html#a1efa5c7b822945d37c93a38667f8d04d">Polyhedron::add</a></div><div class="ttdeci">void add(const Hyperplane&lt; Dim &gt; &amp;v)</div><div class="ttdoc">Append Hyperplane. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:49</div></div>
<div class="ttc" id="classLineSegment_html_a9f97fdceff2bb935c290fb7767e3a696"><div class="ttname"><a href="classLineSegment.html#a9f97fdceff2bb935c290fb7767e3a696">LineSegment::find_ellipsoid</a></div><div class="ttdeci">std::enable_if&lt; U==3 &gt;::type find_ellipsoid(double offset_x)</div><div class="ttdoc">Find ellipsoid in 3D. </div><div class="ttdef"><b>Definition:</b> line_segment.h:136</div></div>
<div class="ttc" id="classDecompBase_html_af3afcc5ba0baa24fa1a52a08138fb67b"><div class="ttname"><a href="classDecompBase.html#af3afcc5ba0baa24fa1a52a08138fb67b">DecompBase::obs_</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; obs_</div><div class="ttdoc">Obstacles, input. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:86</div></div>
<div class="ttc" id="structEllipsoid_html_a70e2f50e23092af77fdc1766efa17c53"><div class="ttname"><a href="structEllipsoid.html#a70e2f50e23092af77fdc1766efa17c53">Ellipsoid::points_inside</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; points_inside(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Calculate points inside ellipsoid, non-exclusive. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:29</div></div>
<div class="ttc" id="classLineSegment_html_a88f0a13c753958dcfc0040d412dcd92a"><div class="ttname"><a href="classLineSegment.html#a88f0a13c753958dcfc0040d412dcd92a">LineSegment::LineSegment</a></div><div class="ttdeci">LineSegment()</div><div class="ttdoc">Simple constructor. </div><div class="ttdef"><b>Definition:</b> line_segment.h:20</div></div>
<div class="ttc" id="data__type_8h_html_afd53e073786661e24985c48b4ef92fcb"><div class="ttname"><a href="data__type_8h.html#afd53e073786661e24985c48b4ef92fcb">Vec3f</a></div><div class="ttdeci">Vecf&lt; 3 &gt; Vec3f</div><div class="ttdoc">Eigen 1D float vector of size 3. </div><div class="ttdef"><b>Definition:</b> data_type.h:79</div></div>
<div class="ttc" id="structEllipsoid_html_ad2c7ee812c96ff94a1a44ddc6a7044b8"><div class="ttname"><a href="structEllipsoid.html#ad2c7ee812c96ff94a1a44ddc6a7044b8">Ellipsoid::closest_point</a></div><div class="ttdeci">Vecf&lt; Dim &gt; closest_point(const vec_Vecf&lt; Dim &gt; &amp;O) const </div><div class="ttdoc">Find the closest point. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:39</div></div>
<div class="ttc" id="geometric__utils_8h_html"><div class="ttname"><a href="geometric__utils_8h.html">geometric_utils.h</a></div><div class="ttdoc">basic geometry utils </div></div>
<div class="ttc" id="data__type_8h_html_a1eeda0bad4efd3be8cb2da1941982410"><div class="ttname"><a href="data__type_8h.html#a1eeda0bad4efd3be8cb2da1941982410">Matf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, M, N &gt; Matf</div><div class="ttdoc">MxN Eigen matrix. </div><div class="ttdef"><b>Definition:</b> data_type.h:63</div></div>
<div class="ttc" id="classLineSegment_html_a3479e2467a33468749971e12aa15c46f"><div class="ttname"><a href="classLineSegment.html#a3479e2467a33468749971e12aa15c46f">LineSegment::dilate</a></div><div class="ttdeci">void dilate(decimal_t radius)</div><div class="ttdoc">Infalte the line segment. </div><div class="ttdef"><b>Definition:</b> line_segment.h:31</div></div>
<div class="ttc" id="decomp__base_8h_html"><div class="ttname"><a href="decomp__base_8h.html">decomp_base.h</a></div><div class="ttdoc">Decomp Base Class. </div></div>
<div class="ttc" id="classLineSegment_html_a0f2e7b18d162be0481f4e5be1cea5363"><div class="ttname"><a href="classLineSegment.html#a0f2e7b18d162be0481f4e5be1cea5363">LineSegment::find_ellipsoid</a></div><div class="ttdeci">std::enable_if&lt; U==2 &gt;::type find_ellipsoid(double offset_x)</div><div class="ttdoc">Find ellipsoid in 2D. </div><div class="ttdef"><b>Definition:</b> line_segment.h:90</div></div>
<div class="ttc" id="data__type_8h_html_a7c99d9360fc6cac2762b786e2fb52266"><div class="ttname"><a href="data__type_8h.html#a7c99d9360fc6cac2762b786e2fb52266">decimal_t</a></div><div class="ttdeci">double decimal_t</div><div class="ttdoc">Rename the float type used in lib. </div><div class="ttdef"><b>Definition:</b> data_type.h:50</div></div>
<div class="ttc" id="data__type_8h_html_a4857a8f36ec316f647bfc006d4799e9a"><div class="ttname"><a href="data__type_8h.html#a4857a8f36ec316f647bfc006d4799e9a">Quatf</a></div><div class="ttdeci">Eigen::Quaternion&lt; decimal_t &gt; Quatf</div><div class="ttdoc">Allias of Eigen::Quaterniond. </div><div class="ttdef"><b>Definition:</b> data_type.h:120</div></div>
<div class="ttc" id="data__type_8h_html_a3a0c45655a5e009e56634ccde0c5c575"><div class="ttname"><a href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a></div><div class="ttdeci">Eigen::Matrix&lt; decimal_t, N, 1 &gt; Vecf</div><div class="ttdoc">Eigen 1D float vector. </div><div class="ttdef"><b>Definition:</b> data_type.h:57</div></div>
<div class="ttc" id="structHyperplane_html"><div class="ttname"><a href="structHyperplane.html">Hyperplane</a></div><div class="ttdoc">Hyperplane class. </div><div class="ttdef"><b>Definition:</b> polyhedron.h:13</div></div>
<div class="ttc" id="classDecompBase_html_a58c41c590f6c92d9fa0886d4d735d7a1"><div class="ttname"><a href="classDecompBase.html#a58c41c590f6c92d9fa0886d4d735d7a1">DecompBase::ellipsoid_</a></div><div class="ttdeci">Ellipsoid&lt; Dim &gt; ellipsoid_</div><div class="ttdoc">Output ellipsoid. </div><div class="ttdef"><b>Definition:</b> decomp_base.h:89</div></div>
<div class="ttc" id="classLineSegment_html_a6c5b5bfafd4b8fd717578f53202a936c"><div class="ttname"><a href="classLineSegment.html#a6c5b5bfafd4b8fd717578f53202a936c">LineSegment::p1_</a></div><div class="ttdeci">Vecf&lt; Dim &gt; p1_</div><div class="ttdoc">One end of line segment, input. </div><div class="ttdef"><b>Definition:</b> line_segment.h:215</div></div>
<div class="ttc" id="structEllipsoid_html_a2cf6b4b66f08415c042be3064d54d6e3"><div class="ttname"><a href="structEllipsoid.html#a2cf6b4b66f08415c042be3064d54d6e3">Ellipsoid::d</a></div><div class="ttdeci">Vecf&lt; Dim &gt; d() const </div><div class="ttdoc">Get center. </div><div class="ttdef"><b>Definition:</b> ellipsoid.h:90</div></div>
<div class="ttc" id="classLineSegment_html_ac2de795a258c1ee25544188033ecc269"><div class="ttname"><a href="classLineSegment.html#ac2de795a258c1ee25544188033ecc269">LineSegment::get_line_segment</a></div><div class="ttdeci">vec_Vecf&lt; Dim &gt; get_line_segment() const </div><div class="ttdoc">Get the line. </div><div class="ttdef"><b>Definition:</b> line_segment.h:38</div></div>
<div class="ttc" id="classLineSegment_html_ac572aa41bea2d3c706d3dd6a1bb24e57"><div class="ttname"><a href="classLineSegment.html#ac572aa41bea2d3c706d3dd6a1bb24e57">LineSegment::add_local_bbox</a></div><div class="ttdeci">void add_local_bbox(Polyhedron&lt; Dim &gt; &amp;Vs)</div><div class="ttdoc">Add the bounding box. </div><div class="ttdef"><b>Definition:</b> line_segment.h:47</div></div>
<div class="ttc" id="classLineSegment_html_a20e5627453b54a1642bac236b41bfb60"><div class="ttname"><a href="classLineSegment.html#a20e5627453b54a1642bac236b41bfb60">LineSegment::LineSegment</a></div><div class="ttdeci">LineSegment(const Vecf&lt; Dim &gt; &amp;p1, const Vecf&lt; Dim &gt; &amp;p2)</div><div class="ttdoc">Basic constructor. </div><div class="ttdef"><b>Definition:</b> line_segment.h:26</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
