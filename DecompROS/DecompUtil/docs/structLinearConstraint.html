<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>MRSL DecompUtil Library: LinearConstraint&lt; Dim &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MRSL DecompUtil Library
   &#160;<span id="projectnumber">0.1</span>
   </div>
   <div id="projectbrief">An implementaion of convex decomposition over point cloud</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structLinearConstraint-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">LinearConstraint&lt; Dim &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>[A, b] for <img class="formulaInl" alt="$Ax &lt; b$" src="form_0.png"/>  
 <a href="structLinearConstraint.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad089df15de12c20098e34f8497212ed9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ad089df15de12c20098e34f8497212ed9"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9">LinearConstraint</a> ()</td></tr>
<tr class="memdesc:ad089df15de12c20098e34f8497212ed9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Null constructor. <br /></td></tr>
<tr class="separator:ad089df15de12c20098e34f8497212ed9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa05fed6e3ac2a531078ec96c174595c7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa05fed6e3ac2a531078ec96c174595c7"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structLinearConstraint.html#aa05fed6e3ac2a531078ec96c174595c7">LinearConstraint</a> (const <a class="el" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf</a>&lt; Dim &gt; &amp;A, const <a class="el" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a> &amp;b)</td></tr>
<tr class="memdesc:aa05fed6e3ac2a531078ec96c174595c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construct from <img class="formulaInl" alt="$A, b$" src="form_1.png"/> directly, s.t <img class="formulaInl" alt="$Ax &lt; b$" src="form_0.png"/>. <br /></td></tr>
<tr class="separator:aa05fed6e3ac2a531078ec96c174595c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81d1602ce0df605f5cb3746ad56c91fd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structLinearConstraint.html#a81d1602ce0df605f5cb3746ad56c91fd">LinearConstraint</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; p0, const <a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt;&gt; &amp;vs)</td></tr>
<tr class="memdesc:a81d1602ce0df605f5cb3746ad56c91fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construct from a inside point and hyperplane array.  <a href="#a81d1602ce0df605f5cb3746ad56c91fd">More...</a><br /></td></tr>
<tr class="separator:a81d1602ce0df605f5cb3746ad56c91fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23779a58e2a1094971cdcceeac3f7f74"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a23779a58e2a1094971cdcceeac3f7f74"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structLinearConstraint.html#a23779a58e2a1094971cdcceeac3f7f74">inside</a> (const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt; &amp;pt)</td></tr>
<tr class="memdesc:a23779a58e2a1094971cdcceeac3f7f74"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if the point is inside polyhedron using linear constraint. <br /></td></tr>
<tr class="separator:a23779a58e2a1094971cdcceeac3f7f74"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a33d750d5c3558564263959c031a7b3bc"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a33d750d5c3558564263959c031a7b3bc"></a>
<a class="el" href="data__type_8h.html#a44c975fba9ebd61e295d78215b6569c3">MatDNf</a>&lt; Dim &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>A_</b></td></tr>
<tr class="separator:a33d750d5c3558564263959c031a7b3bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e3d195e7c647448bf1a25defbf9b0ec"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a0e3d195e7c647448bf1a25defbf9b0ec"></a>
<a class="el" href="data__type_8h.html#af9c7300efe3567726a373210d4dbc046">VecDf</a>&#160;</td><td class="memItemRight" valign="bottom"><b>b_</b></td></tr>
<tr class="separator:a0e3d195e7c647448bf1a25defbf9b0ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;int Dim&gt;<br />
struct LinearConstraint&lt; Dim &gt;</h3>

<p>[A, b] for <img class="formulaInl" alt="$Ax &lt; b$" src="form_0.png"/> </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a81d1602ce0df605f5cb3746ad56c91fd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;int Dim&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structLinearConstraint.html">LinearConstraint</a>&lt; Dim &gt;::<a class="el" href="structLinearConstraint.html">LinearConstraint</a> </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="data__type_8h.html#a3a0c45655a5e009e56634ccde0c5c575">Vecf</a>&lt; Dim &gt;&#160;</td>
          <td class="paramname"><em>p0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="data__type_8h.html#a30c607180de5bc1b7c30f5cbaf9b188b">vec_E</a>&lt; <a class="el" href="structHyperplane.html">Hyperplane</a>&lt; Dim &gt;&gt; &amp;&#160;</td>
          <td class="paramname"><em>vs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Construct from a inside point and hyperplane array. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">p0</td><td>point that is inside </td></tr>
    <tr><td class="paramname">vs</td><td>hyperplane array, normal should go outside </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>include/decomp_geometry/<a class="el" href="polyhedron_8h_source.html">polyhedron.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
