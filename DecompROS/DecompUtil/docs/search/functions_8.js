var searchData=
[
  ['line_5fintersect',['line_intersect',['../geometric__utils_8h.html#a79027aaed3d36e00b00eb58e55d95f16',1,'geometric_utils.h']]],
  ['line_5fintersects',['line_intersects',['../geometric__utils_8h.html#a34493497e7d12d4a99e6b9b7cef007f8',1,'geometric_utils.h']]],
  ['linearconstraint',['LinearConstraint',['../structLinearConstraint.html#ad089df15de12c20098e34f8497212ed9',1,'LinearConstraint::LinearConstraint()'],['../structLinearConstraint.html#aa05fed6e3ac2a531078ec96c174595c7',1,'LinearConstraint::LinearConstraint(const MatDNf&lt; Dim &gt; &amp;A, const VecDf &amp;b)'],['../structLinearConstraint.html#a81d1602ce0df605f5cb3746ad56c91fd',1,'LinearConstraint::LinearConstraint(const Vecf&lt; Dim &gt; p0, const vec_E&lt; Hyperplane&lt; Dim &gt;&gt; &amp;vs)']]],
  ['linesegment',['LineSegment',['../classLineSegment.html#a88f0a13c753958dcfc0040d412dcd92a',1,'LineSegment::LineSegment()'],['../classLineSegment.html#a20e5627453b54a1642bac236b41bfb60',1,'LineSegment::LineSegment(const Vecf&lt; Dim &gt; &amp;p1, const Vecf&lt; Dim &gt; &amp;p2)']]]
];
