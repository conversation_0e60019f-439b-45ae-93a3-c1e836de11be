#!/bin/bash
# 简化的Btraj启动脚本 - 专注解决X11问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "简化Btraj启动脚本"

# 1. 设置ROS环境
info "设置ROS环境..."
source /opt/ros/noetic/setup.bash
if [ -f /workspace/catkin_ws/devel/setup.bash ]; then
    source /workspace/catkin_ws/devel/setup.bash
    success "ROS环境已加载"
else
    error "工作空间未构建，请先运行: build-workspace.sh"
    exit 1
fi

# 2. 设置X11环境 - 多种配置尝试
info "设置X11环境..."

# 基础X11设置
export DISPLAY=${DISPLAY:-:0}
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=1
export XDG_RUNTIME_DIR=/tmp/runtime-btraj
mkdir -p $XDG_RUNTIME_DIR && chmod 700 $XDG_RUNTIME_DIR

# 额外的Qt/RViz兼容性设置
export QT_GRAPHICSSYSTEM=native
export QT_QUICK_BACKEND=software
export LIBGL_ALWAYS_SOFTWARE=1

info "X11环境变量:"
echo "  DISPLAY=$DISPLAY"
echo "  QT_X11_NO_MITSHM=$QT_X11_NO_MITSHM"
echo "  LIBGL_ALWAYS_INDIRECT=$LIBGL_ALWAYS_INDIRECT"
echo "  LIBGL_ALWAYS_SOFTWARE=$LIBGL_ALWAYS_SOFTWARE"

# 3. 检查X11连接
info "检查X11连接..."
if [ -S "/tmp/.X11-unix/X0" ]; then
    success "X11套接字存在"
else
    warn "X11套接字不存在，但继续尝试"
fi

# 4. 启动选项
echo ""
info "启动选项:"
echo "1) 启动完整仿真 (推荐)"
echo "2) 仅启动ROS节点 (无GUI)"
echo "3) 测试RViz"
echo "4) 环境诊断"
echo -n "请选择 (1-4): "
read choice

case $choice in
    1)
        info "启动完整Btraj仿真..."
        info "如果RViz无法显示，请检查NoMachine连接"
        
        # 启动roscore在后台
        info "启动ROS Master..."
        roscore &
        sleep 3
        
        # 启动仿真
        info "启动仿真节点..."
        exec roslaunch bezier_planer simulation.launch
        ;;
    2)
        info "启动ROS节点 (无GUI)..."
        # 创建无GUI版本的launch文件
        temp_launch="/tmp/btraj_no_gui.launch"
        cat > "$temp_launch" << 'EOF'
<launch>
<arg name="map_size_x" default="50.0"/>
<arg name="map_size_y" default="50.0"/>
<arg name="map_size_z" default=" 5.0"/>
<arg name="init_x" default="-20.0"/>
<arg name="init_y" default="-20.0"/>
<arg name="init_z" default="  0.5"/>

  <node pkg="bezier_planer" type="b_traj_node" name="b_traj_node" output="screen">
      <remap from="~waypoints"      to="/waypoint_generator/waypoints"/>
      <remap from="~odometry"       to="/odom/fake_odom"/>
      <remap from="~map"            to="/random_forest_sensing/random_forest"/> 
      <remap from="~command"        to="/position_cmd"/> 
      <param name="optimization/poly_order"  value="8"/> 
      <param name="optimization/min_order"   value="2.5"/> 
      <param name="map/x_size"       value="$(arg map_size_x)"/>
      <param name="map/y_size"       value="$(arg map_size_y)"/>
      <param name="map/z_size"       value="$(arg map_size_z)"/>
      <param name="map/margin"       value="0.2" />
      <param name="planning/init_x"  value="$(arg init_x)"/>
      <param name="planning/init_y"  value="$(arg init_y)"/>
      <param name="planning/init_z"  value="$(arg init_z)"/>
      <param name="planning/max_vel" value="2.0"/>
      <param name="planning/max_acc" value="2.0"/>
  </node>
  
  <node pkg="bezier_planer" type="odom_generator" output="screen" name="odom_generator">    
      <remap from="~odometry"  to="odom/fake_odom"/>
      <remap from="~command"   to="/position_cmd"/>
      <param name="init_x"  value="$(arg init_x)"/>
      <param name="init_y"  value="$(arg init_y)"/>
      <param name="init_z"  value="$(arg init_z)"/>
  </node>

  <node pkg="bezier_planer" type="random_forest_sensing" name="random_forest_sensing" output="screen">    
      <remap from="~odometry"   to="/odom/fake_odom"/>    
      <param name="init_state_x"   value="$(arg init_x)"/>
      <param name="init_state_y"   value="$(arg init_y)"/>
      <param name="map/x_size"     value="$(arg map_size_x)" />
      <param name="map/y_size"     value="$(arg map_size_y)" />
      <param name="map/z_size"     value="$(arg map_size_z)" />
      <param name="map/obs_num"    value="520"/>        
      <param name="map/resolution" value="0.2"/>        
  </node>
</launch>
EOF
        roslaunch "$temp_launch"
        ;;
    3)
        info "测试RViz..."
        rviz || error "RViz启动失败"
        ;;
    4)
        info "运行环境诊断..."
        test-x11.sh
        ;;
    *)
        error "无效选择"
        exit 1
        ;;
esac
