#!/bin/bash
# Btraj环境测试脚本 - 验证所有依赖是否正确安装

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "Btraj环境测试开始..."

# 测试计数器
total_tests=0
passed_tests=0

test_item() {
    local name="$1"
    local command="$2"
    total_tests=$((total_tests + 1))
    
    if eval "$command" >/dev/null 2>&1; then
        success "✓ $name"
        passed_tests=$((passed_tests + 1))
    else
        error "✗ $name"
    fi
}

echo ""
info "=== 基础环境测试 ==="

test_item "ROS Noetic" "[ -n \"\$ROS_DISTRO\" ] && [ \"\$ROS_DISTRO\" = \"noetic\" ]"
test_item "roscore命令" "command -v roscore"
test_item "catkin_make命令" "command -v catkin_make"

echo ""
info "=== 工作空间测试 ==="

test_item "Btraj源码" "[ -d /workspace/catkin_ws/src/Btraj ]"
test_item "plan_utils源码" "[ -d /workspace/catkin_ws/src/plan_utils ]"
test_item "quadrotor_msgs" "[ -d /workspace/catkin_ws/src/plan_utils/quadrotor_msgs ]"

echo ""
info "=== 依赖库测试 ==="

test_item "Eigen3" "pkg-config --exists eigen3"
test_item "PCL" "pkg-config --exists pcl_common-1.10"
test_item "OpenCV" "pkg-config --exists opencv4"

echo ""
info "=== Mosek测试 ==="

test_item "Mosek许可证文件" "[ -f /root/mosek/mosek.lic ]"
test_item "Mosek环境变量" "[ -n \"\$MOSEKLM_LICENSE_FILE\" ]"

echo ""
info "=== X11/GUI测试 ==="

test_item "DISPLAY环境变量" "[ -n \"\$DISPLAY\" ]"
test_item "X11套接字" "[ -S /tmp/.X11-unix/X0 ]"

echo ""
info "=== 构建测试 ==="

if [ -f /workspace/catkin_ws/devel/setup.bash ]; then
    test_item "工作空间已构建" "true"
    source /workspace/catkin_ws/devel/setup.bash
    test_item "bezier_planer包" "rospack find bezier_planer"
else
    test_item "工作空间已构建" "false"
    warn "工作空间未构建，运行 build-workspace.sh 进行构建"
fi

echo ""
info "=== 测试结果 ==="
echo "通过: $passed_tests/$total_tests"

if [ $passed_tests -eq $total_tests ]; then
    success "🎉 所有测试通过！Btraj环境配置正确"
    echo ""
    info "下一步："
    echo "1. 如果工作空间未构建，运行: build-workspace.sh"
    echo "2. 启动仿真: start-btraj-sim.sh"
elif [ $passed_tests -gt $((total_tests * 3 / 4)) ]; then
    warn "⚠️  大部分测试通过，可以尝试运行Btraj"
    echo "失败的测试可能不会影响基本功能"
else
    error "❌ 多个关键测试失败，请检查配置"
    echo "建议重新构建容器或检查挂载配置"
fi

echo ""
info "测试完成"
