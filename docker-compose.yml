version: '3.8'

services:
  ros-noetic:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ros-noetic-gui
    hostname: ros-noetic
    
    # 网络配置 - 使用host网络便于ROS通信
    network_mode: host
    
    # 特权模式 - GUI和硬件访问需要
    privileged: true
    
    # 环境变量
    environment:
      - DISPLAY=${DISPLAY:-:0}
      - QT_X11_NO_MITSHM=1
      - XAUTHORITY=/tmp/.docker.xauth
      - ROS_HOSTNAME=localhost
      - ROS_MASTER_URI=http://localhost:11311
      # Mosek环境变量
      - MOSEKLM_LICENSE_FILE=/home/<USER>/mosek/mosek.lic

    # 卷挂载
    volumes:
      # X11显示支持
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /tmp/.docker.xauth:/tmp/.docker.xauth:rw

      # GPU支持 (可选)
      - /dev/dri:/dev/dri

      # 共享内存 (提高GUI性能)
      - /dev/shm:/dev/shm

      # Btraj项目挂载
      - ./Btraj:/home/<USER>/catkin_ws/src/Btraj:rw
      - ./plan_utils:/home/<USER>/catkin_ws/src/plan_utils:rw
      - ./mosek:/home/<USER>/mosek:rw

      # 数据持久化
      - ./docker_data/catkin_build:/home/<USER>/catkin_ws/build:rw
      - ./docker_data/catkin_devel:/home/<USER>/catkin_ws/devel:rw
      - ./docker_data/bash_history:/home/<USER>/.bash_history:rw
    
    # 工作目录
    working_dir: /home/<USER>/catkin_ws

    # 用户设置
    user: rosuser

    # 保持容器运行
    stdin_open: true
    tty: true

    # 启动命令
    command: >
      bash -c "
        echo '=== Btraj ROS 开发环境 ===' &&
        echo 'DISPLAY: ${DISPLAY}' &&
        echo 'XAUTHORITY: ${XAUTHORITY}' &&
        echo 'ROS_MASTER_URI: ${ROS_MASTER_URI}' &&
        echo 'MOSEKLM_LICENSE_FILE: ${MOSEKLM_LICENSE_FILE}' &&
        echo '' &&
        echo '工作目录: ~/catkin_ws' &&
        echo '' &&
        echo '可用命令:' &&
        echo '  ./test-btraj-env.sh  - 测试Btraj环境' &&
        echo '  ./build-btraj.sh     - 构建Btraj工作空间' &&
        echo '  ./start-btraj.sh     - 启动Btraj仿真' &&
        echo '  roscore              - 启动ROS Master' &&
        echo '  rviz                 - 启动RViz' &&
        echo '' &&
        echo '项目状态:' &&
        echo '  Btraj: $([ -d src/Btraj ] && echo \"已挂载\" || echo \"未挂载\")' &&
        echo '  plan_utils: $([ -d src/plan_utils ] && echo \"已挂载\" || echo \"未挂载\")' &&
        echo '  Mosek: $([ -f ~/mosek/mosek.lic ] && echo \"许可证存在\" || echo \"许可证缺失\")' &&
        echo '' &&
        exec bash
      "
