version: '3.8'

services:
  ros-noetic:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ros-noetic-gui
    hostname: ros-noetic
    
    # 网络配置 - 使用host网络便于ROS通信
    network_mode: host
    
    # 特权模式 - GUI和硬件访问需要
    privileged: true
    
    # 环境变量
    environment:
      - DISPLAY=${DISPLAY:-:0}
      - QT_X11_NO_MITSHM=1
      - XAUTHORITY=/tmp/.docker.xauth
      - ROS_HOSTNAME=localhost
      - ROS_MASTER_URI=http://localhost:11311

    # 卷挂载
    volumes:
      # X11显示支持
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /tmp/.docker.xauth:/tmp/.docker.xauth:rw
      
      # GPU支持 (可选)
      - /dev/dri:/dev/dri
      
      # 共享内存 (提高GUI性能)
      - /dev/shm:/dev/shm
    
    # 工作目录
    working_dir: /home/<USER>

    # 用户设置
    user: rosuser

    # 保持容器运行
    stdin_open: true
    tty: true

    # 启动命令
    command: >
      bash -c "
        echo '=== ROS Noetic GUI 测试环境 ===' &&
        echo 'DISPLAY: ${DISPLAY}' &&
        echo 'XAUTHORITY: ${XAUTHORITY}' &&
        echo 'ROS_MASTER_URI: ${ROS_MASTER_URI}' &&
        echo '' &&
        echo '可用命令:' &&
        echo '  roscore          - 启动ROS Master' &&
        echo '  rviz             - 启动RViz' &&
        echo '  rqt              - 启动RQt工具' &&
        echo '  ./test-gui.sh    - 测试GUI环境' &&
        echo '' &&
        exec bash
      "
