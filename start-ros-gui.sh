#!/bin/bash
# ROS Noetic GUI 启动脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "ROS Noetic GUI 环境启动脚本"

# 检查Docker和Docker Compose
if ! command -v docker >/dev/null 2>&1; then
    error "Docker未安装或不可用"
    exit 1
fi

if ! (command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1); then
    error "Docker Compose未安装或不可用"
    exit 1
fi

# 检查DISPLAY环境变量
if [ -z "$DISPLAY" ]; then
    warn "DISPLAY环境变量未设置，使用默认值 :0"
    export DISPLAY=:0
fi

info "当前DISPLAY: $DISPLAY"

# 检查X11套接字
if [ ! -S "/tmp/.X11-unix/X0" ]; then
    warn "X11套接字不存在，请确保:"
    echo "  1. NoMachine正在运行"
    echo "  2. 已连接到Linux桌面"
    echo "  3. DISPLAY环境变量正确"
fi

# 检查.Xauthority文件
if [ ! -f "$HOME/.Xauthority" ]; then
    warn ".Xauthority文件不存在: $HOME/.Xauthority"
    echo "  这可能会导致X11认证问题"
fi

echo ""
info "启动选项:"
echo "1) 构建并启动容器"
echo "2) 启动现有容器"
echo "3) 停止容器"
echo "4) 重新构建容器"
echo "5) 查看容器日志"
echo "6) 进入容器shell"
echo -n "请选择 (1-6): "
read choice

case $choice in
    1)
        info "构建并启动ROS Noetic GUI容器..."
        docker compose up --build -d
        success "容器已启动"
        info "使用以下命令进入容器:"
        echo "  docker compose exec ros-noetic bash"
        echo "  或运行: $0 并选择选项6"
        ;;
    2)
        info "启动现有容器..."
        docker compose up -d
        success "容器已启动"
        ;;
    3)
        info "停止容器..."
        docker compose down
        success "容器已停止"
        ;;
    4)
        info "重新构建容器..."
        docker compose down
        docker compose build --no-cache
        docker compose up -d
        success "容器已重新构建并启动"
        ;;
    5)
        info "查看容器日志..."
        docker compose logs -f ros-noetic
        ;;
    6)
        info "进入容器shell..."
        if docker compose ps | grep -q "ros-noetic.*Up"; then
            docker compose exec ros-noetic bash
        else
            error "容器未运行，请先启动容器"
            exit 1
        fi
        ;;
    *)
        error "无效选择"
        exit 1
        ;;
esac

echo ""
info "容器管理命令:"
echo "  启动: docker compose up -d"
echo "  停止: docker compose down"
echo "  进入: docker compose exec ros-noetic bash"
echo "  日志: docker compose logs ros-noetic"

echo ""
info "容器内测试命令:"
echo "  test-gui.sh  - 测试GUI环境"
echo "  roscore      - 启动ROS Master"
echo "  rviz         - 启动RViz"
echo "  rqt          - 启动RQt工具"
