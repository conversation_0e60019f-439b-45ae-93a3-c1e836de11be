#!/bin/bash
# 快速测试Docker Compose配置

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "Docker Compose ROS GUI 环境测试"

# 检查必要的文件
info "检查配置文件..."
required_files=("docker-compose.yml" "Dockerfile" "start-ros-gui.sh")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        success "✓ $file 存在"
    else
        error "✗ $file 不存在"
        exit 1
    fi
done

# 检查Docker
info "检查Docker环境..."
if command -v docker >/dev/null 2>&1; then
    success "✓ Docker 可用"
    docker --version
else
    error "✗ Docker 不可用"
    exit 1
fi

if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    success "✓ Docker Compose 可用"
    docker compose version
else
    error "✗ Docker Compose 不可用"
    exit 1
fi

# 检查X11环境
info "检查X11环境..."
echo "DISPLAY: ${DISPLAY:-未设置}"

if [ -n "$DISPLAY" ]; then
    success "✓ DISPLAY 已设置"
else
    warn "⚠ DISPLAY 未设置，将使用默认值 :0"
fi

if [ -S "/tmp/.X11-unix/X0" ]; then
    success "✓ X11套接字存在"
else
    warn "⚠ X11套接字不存在，GUI可能无法显示"
fi

if [ -f "$HOME/.Xauthority" ]; then
    success "✓ X认证文件存在"
else
    warn "⚠ X认证文件不存在，可能影响GUI显示"
fi

# 验证docker-compose配置
info "验证Docker Compose配置..."
if docker compose config >/dev/null 2>&1; then
    success "✓ Docker Compose配置有效"
else
    error "✗ Docker Compose配置无效"
    docker compose config
    exit 1
fi

echo ""
info "=== 环境检查完成 ==="
echo ""
info "下一步操作:"
echo "1. 启动环境: ./start-ros-gui.sh"
echo "2. 选择选项1构建并启动容器"
echo "3. 选择选项6进入容器"
echo "4. 在容器内运行: test-gui.sh"
echo "5. 测试RViz: roscore & 然后 rviz"

echo ""
info "如果遇到问题，请检查:"
echo "- NoMachine是否正在运行"
echo "- 是否已连接到Linux桌面"
echo "- DISPLAY环境变量是否正确"

echo ""
success "环境检查完成！可以开始测试了。"
