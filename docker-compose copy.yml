version: '3.8'

services:
  ros-super:
    build: .
    container_name: ros-super-container
    hostname: ros-super
    
    # 网络配置
    network_mode: host
    
    # 环境变量
    environment:
      - DISPLAY=${DISPLAY}
      - QT_X11_NO_MITSHM=1
      - XAUTHORITY=/tmp/.docker.xauth
      - ROS_HOSTNAME=localhost
      - ROS_MASTER_URI=http://localhost:11311
    
    # 卷挂载
    volumes:
      # X11转发支持
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /tmp/.docker.xauth:/tmp/.docker.xauth:rw
      
      # 项目代码挂载（使用现有的SUPER项目）
      - ./SUPER:/home/<USER>/super_ws/src/SUPER:rw
      
      # 数据持久化目录（用户配置备份）
      - ./docker_data/config:/home/<USER>/config_backup:rw
      
      # 保持用户配置
      - ./docker_data/home_config:/home/<USER>/.config:rw
      - ./docker_data/zsh_history:/home/<USER>/.zsh_history:rw
      - ./docker_data/bash_history:/home/<USER>/.bash_history:rw
      
      # catkin工作空间持久化（除了src，其他编译产物也保持）
      - ./docker_data/catkin_build:/home/<USER>/super_ws/build:rw
      - ./docker_data/catkin_devel:/home/<USER>/super_ws/devel:rw
      - ./docker_data/catkin_install:/home/<USER>/super_ws/install:rw
    
    # 设备访问
    devices:
      - /dev/dri:/dev/dri  # GPU访问（如果有）
    
    # 特权模式（用于硬件访问）
    privileged: true
    
    # 保持容器运行
    stdin_open: true
    tty: true
    
    # 工作目录
    working_dir: /home/<USER>/super_ws
    
    # 重启策略
    restart: unless-stopped
