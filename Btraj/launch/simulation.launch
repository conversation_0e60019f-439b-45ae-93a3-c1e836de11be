<launch>

<arg name="map_size_x" default="50.0"/>
<arg name="map_size_y" default="50.0"/>
<arg name="map_size_z" default=" 5.0"/>

<arg name="init_x" default="-20.0"/>
<arg name="init_y" default="-20.0"/>
<arg name="init_z" default="  0.5"/>

  <node pkg="bezier_planer" type="b_traj_node" name="b_traj_node" output="screen">
      <remap from="~waypoints"      to="/waypoint_generator/waypoints"/>
      <remap from="~odometry"       to="/odom/fake_odom"/>
      <remap from="~map"            to="/random_forest_sensing/random_forest"/> 
      <remap from="~command"        to="/position_cmd"/> 
      <param name="optimization/poly_order"  value="8"/> 
      <param name="optimization/min_order"   value="2.5"/> 
      <param name="map/x_size"       value="$(arg map_size_x)"/>
      <param name="map/y_size"       value="$(arg map_size_y)"/>
      <param name="map/z_size"       value="$(arg map_size_z)"/>
      
      <param name="map/z_local_size" value="16.0"/>
      <param name="map/y_local_size" value="16.0"/>
      <param name="map/z_local_size" value="8.0" />

      <param name="map/margin"       value="0.2" />
      <param name="planning/init_x"  value="$(arg init_x)"/>
      <param name="planning/init_y"  value="$(arg init_y)"/>
      <param name="planning/init_z"  value="$(arg init_z)"/>
      <param name="planning/inflate_iter"  value="200"  />
      <param name="planning/step_length"   value="1"    />
      <param name="planning/cube_margin"   value="0.0"  />
      <param name="planning/max_vel"       value="2.0"  />
      <param name="planning/max_acc"       value="2.0"  />
      <param name="planning/check_horizon" value="10.0" />
      <param name="planning/stop_horizon"  value=" 3.0" />
      <param name="planning/is_limit_vel"  value="true" />
      <param name="planning/is_limit_acc"  value="false"/>
      <param name="planning/is_use_fm"     value="true" />
      <param name="vis/vis_traj_width" value="0.15"/>
      <param name="vis/is_proj_cube"   value="false"/>
  </node>
  
  <node pkg  = "bezier_planer" type = "odom_generator" output = "screen" name = "odom_generator">    
      <remap from = "~odometry"  to = "odom/fake_odom"/>
      <remap from = "~command"   to = "/position_cmd"/>
      <param name="init_x"  value="$(arg init_x)"/>
      <param name="init_y"  value="$(arg init_y)"/>
      <param name="init_z"  value="$(arg init_z)"/>
  </node>

  <node pkg  = "bezier_planer" type = "b_traj_server" output = "screen" name = "b_traj_server">    
      <remap from = "~trajectory"        to = "b_traj_node/trajectory"/>
      <remap from = "~odometry"          to = "/odom/fake_odom"/>
      <remap from = "~position_command"  to = "/position_cmd"/>
      <param name="optimization/poly_order_min" value="3" /> 
      <param name="optimization/poly_order_max" value="12"/> 
  </node>

  <node pkg="waypoint_generator" name="waypoint_generator" type="waypoint_generator" output="screen">
      <remap from="~odom" to="/odom/fake_odom"/>        
      <remap from="~goal" to="/goal"/>
      <param name="waypoint_type" value="manual-lonely-waypoint"/>    
  </node>

  <node pkg ="bezier_planer" name ="random_forest_sensing" type ="random_forest_sensing" output = "screen">    
      <remap from="~odometry"   to="/odom/fake_odom"/>    
      <param name="init_state_x"   value="$(arg init_x)"/>
      <param name="init_state_y"   value="$(arg init_y)"/>
      <param name="map/x_size"     value="$(arg map_size_x)" />
      <param name="map/y_size"     value="$(arg map_size_y)" />
      <param name="map/z_size"     value="$(arg map_size_z)" />
      <param name="map/obs_num"    value="520"/>        
      <param name="map/resolution" value="0.2"/>        
      <param name="ObstacleShape/lower_rad" value="0.3"/>
      <param name="ObstacleShape/upper_rad" value="1.6"/>
      <param name="ObstacleShape/lower_hei" value="1.0"/>
      <param name="ObstacleShape/upper_hei" value="6.0"/>        
      <param name="sensing/radius" value="15.0"/>        
      <param name="sensing/rate"   value="10.0"/>        
  </node>

  <node pkg="odom_visualization" name="odom_visualization_ukf_" type="odom_visualization" output="screen">
      <remap from="~odom" to="/odom/fake_odom"/>
      <param name="color/a" value="0.8"/>    
      <param name="color/r" value="0.0"/>        
      <param name="color/g" value="0.0"/>        
      <param name="color/b" value="0.0"/>       
      <param name="covariance_scale" value="100.0"/>       
      <param name="robot_scale" value="1.0"/>   
  </node>

  <node name="rvizvisualisation" pkg="rviz" type="rviz" output="log" args="-d $(find bezier_planer)/launch/rviz_config/simulation.rviz" />

</launch>