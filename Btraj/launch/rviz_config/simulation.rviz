Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Grid1/Offset1
        - /AllMap1/Autocompute Value Bounds1
      Splitter Ratio: 0.572222233
    Tree Height: 665
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded: ~
    Name: Tool Properties
    Splitter Ratio: 0.588679016
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: LocalMap
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 0.200000003
      Class: rviz/Grid
      Color: 0; 0; 0
      Enabled: true
      Line Style:
        Line Width: 0.0299999993
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 250
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz/Axes
      Enabled: true
      Length: 5
      Name: Axes
      Radius: 0.100000001
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 0.200000003
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 3.9000001
        Min Value: 0
        Value: false
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 193; 193; 193
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: AllMap
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.200000003
      Style: Boxes
      Topic: /random_forest_sensing/all_map
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 85; 0; 0
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: SensedObs
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.200000003
      Style: Boxes
      Topic: /random_forest_sensing/random_forest
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 5.9000001
        Min Value: 0
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: FlatColor
      Decay Time: 0
      Enabled: false
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: InflateMap
      Position Transformer: XYZ
      Queue Size: 1
      Selectable: false
      Size (Pixels): 5
      Size (m): 0.200000003
      Style: Boxes
      Topic: /b_traj_node/vis_map_inflate
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: false
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 5.9000001
        Min Value: 0.100000001
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz/PointCloud2
      Color: 255; 255; 255
      Color Transformer: AxisColor
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: LocalMap
      Position Transformer: XYZ
      Queue Size: 10
      Selectable: false
      Size (Pixels): 3
      Size (m): 0.200000003
      Style: Boxes
      Topic: /b_traj_node/vis_map_local
      Unreliable: false
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /odom_visualization_ukf_/robot
      Name: Robot
      Namespaces:
        mesh: true
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /b_traj_node/corridor_vis
      Name: Corridor
      Namespaces:
        corridor: true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_node/check_trajectory
      Name: CheckTraj
      Namespaces:
        trajectory/check_trajectory: true
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /b_traj_node/path_vis
      Name: Path
      Namespaces:
        b_traj/fast_marching_path: true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_node/trajectory_vis
      Name: Trajectory
      Namespaces:
        trajectory/trajectory: true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_node/stop_trajectory
      Name: StopTraj
      Namespaces:
        trajectory/stop_trajectory: true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_node/expanded_nodes_vis
      Name: ExpNodes
      Namespaces:
        {}
      Queue Size: 100
      Value: true
    - Class: rviz/MarkerArray
      Enabled: true
      Marker Topic: /b_traj_node/grid_path_vis
      Name: GridPath
      Namespaces:
        {}
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_server/desired_acceleration
      Name: Acc
      Namespaces:
        acc: true
      Queue Size: 100
      Value: true
    - Class: rviz/Marker
      Enabled: true
      Marker Topic: /b_traj_server/desired_velocity
      Name: Vel
      Namespaces:
        vel: true
      Queue Size: 100
      Value: true
  Enabled: true
  Global Options:
    Background Color: 255; 251; 188
    Default Light: true
    Fixed Frame: world
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/FocusCamera
    - Class: rviz_plugins/Goal3DTool
      Topic: goal
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 42.5200272
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.0599999987
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0.979101241
        Y: 9.91577625
        Z: -12.7958317
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.0500000007
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.00999999978
      Pitch: 1.4947964
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 4.75939035
    Saved:
      - Class: rviz/Orbit
        Distance: 79.2448425
        Enable Stereo Rendering:
          Stereo Eye Separation: 0.0599999987
          Stereo Focal Distance: 1
          Swap Stereo Eyes: false
          Value: false
        Focal Point:
          X: -13.6964359
          Y: -5.96537924
          Z: -19.0381603
        Focal Shape Fixed Size: true
        Focal Shape Size: 0.0500000007
        Invert Z Axis: false
        Name: Orbit
        Near Clip Distance: 0.00999999978
        Pitch: 1.1997968
        Target Frame: <Fixed Frame>
        Value: Orbit (rviz)
        Yaw: 4.55750179
Window Geometry:
  Displays:
    collapsed: false
  Height: 807
  Hide Left Dock: false
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd00000004000000000000016a000002d3fc020000000bfb000000100044006900730070006c006100790073010000003a000002d3000000c600fffffffb0000000a0056006900650077007300000002aa000001580000009e00fffffffb0000001200530065006c0065006300740069006f006e00000000000000009b0000005c00fffffffb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb0000001e0054006f006f006c002000500072006f007000650072007400690065007300000000e3000001c60000005c00fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000000a0049006d006100670065020000037e000000e30000016a00000089fb0000000a0049006d0061006700650200000593000001f50000016a000000ed000000010000010f000002c4fc0200000002fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000001200530065006c0065006300740069006f006e010000025a000000b20000000000000000000000020000073f000000ddfc0100000002fb0000000c00430061006d006500720061020000012e00000138000002d9000001a9fb0000000a00560069006500770073030000004e00000080000002e100000197000000030000041d0000003efc0100000002fb0000000800540069006d006500000000000000041d0000024400fffffffb0000000800540069006d006501000000000000045000000000000000000000047a000002d300000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1514
  X: 182
  Y: 88
