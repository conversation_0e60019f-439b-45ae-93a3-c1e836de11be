<?xml version="1.0"?>
<package>
  <name>sdf_tools</name>
  <version>0.0.1</version>
  <description>Builds 2D signed distance fields from images, 3D signed distance fields from MoveIt PlanningScene/Octomap, provides a lightweight signed distance field library, message types for signed distance fields, and tools to compress signed distance fields for transport and storage.</description>
  <maintainer email="<EMAIL>"><PERSON>-<PERSON></maintainer>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>image_transport</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>moveit_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>moveit_core</build_depend>
  <build_depend>moveit_ros_planning</build_depend>
  <build_depend>arc_utilities</build_depend>
  <build_depend>message_generation</build_depend>

  <run_depend>image_transport</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>moveit_msgs</run_depend>
  <run_depend>visualization_msgs</run_depend>
  <run_depend>cv_bridge</run_depend>
  <run_depend>moveit_core</run_depend>
  <run_depend>moveit_ros_planning</run_depend>
  <run_depend>arc_utilities</run_depend>
  <run_depend>message_runtime</run_depend>


  <!-- The export tag contains other, unspecified, tags -->
  <export>
    <!-- You can specify that this package is a metapackage here: -->
    <!-- <metapackage/> -->

    <!-- Other tools can request additional information be placed here -->

  </export>
</package>
