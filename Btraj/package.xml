<?xml version="1.0"?>
<package>
  <name>bezier_planer</name>
  <version>0.0.1</version>
  <description> trajectory generation in 3d space for drones </description>
  <maintainer email="<EMAIL>">fgaoaa</maintainer>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>image_transport</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>arc_utilities</build_depend>
  <build_depend>sdf_tools</build_depend>
  <build_depend>message_generation</build_depend>

  <run_depend>image_transport</run_depend>
  <run_depend>roscpp</run_depend>
  <run_depend>rospy</run_depend>
  <run_depend>sensor_msgs</run_depend>
  <run_depend>std_msgs</run_depend>
  <run_depend>visualization_msgs</run_depend>
  <run_depend>cv_bridge</run_depend>
  <build_depend>arc_utilities</build_depend>
  <build_depend>sdf_tools</build_depend>
  <run_depend>message_runtime</run_depend>

  <export>

  </export>
</package>
