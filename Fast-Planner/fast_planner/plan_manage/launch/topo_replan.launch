<launch>
  <!-- global parameters -->
  <arg name="map_size_x" value="40.0"/>
  <arg name="map_size_y" value="20.0"/>
  <arg name="map_size_z" value=" 5.0"/>

  <arg name="odom_topic" value="/state_ukf/odom" />

  <!-- main algorithm params -->
  <include file="$(find plan_manage)/launch/topo_algorithm.xml">
    <!-- size of map -->
    <arg name="map_size_x_" value="$(arg map_size_x)"/>
    <arg name="map_size_y_" value="$(arg map_size_y)"/>
    <arg name="map_size_z_" value="$(arg map_size_z)"/>
    <arg name="odometry_topic" value="$(arg odom_topic)"/>

    <!-- camera pose: transform of camera frame in the world frame -->
    <!-- depth topic: depth image, 640x480 by default -->
    <!-- don't set cloud_topic if you already set these ones! -->
    <arg name="camera_pose_topic" value="/pcl_render_node/camera_pose"/>
    <arg name="depth_topic" value="/pcl_render_node/depth"/>

    <!-- topic of point cloud measurement, such as from LIDAR  -->
    <!-- don't set camera pose and depth, if you already set this one! -->
    <arg name="cloud_topic" value="/pcl_render_node/cloud"/>

    <!-- intrinsic params of the depth camera -->
    <arg name="cx" value="321.04638671875"/>
    <arg name="cy" value="243.44969177246094"/>
    <arg name="fx" value="387.229248046875"/>
    <arg name="fy" value="387.229248046875"/>

    <!-- maximum velocity and acceleration the drone will reach -->
    <arg name="max_vel" value="3.0" />
    <arg name="max_acc" value="2.5" />

    <!-- 1: use 2D Nav Goal to select goal  -->
    <!-- 2: use global waypoints below  -->
    <!-- 3: use global waypoints below to set reference path --> 
    <arg name="flight_type" value="2" />

    <!-- global waypoints -->
    <arg name="point_num" value="3" />

    <arg name="point0_x" value="19.0" />
    <arg name="point0_y" value="0.0" />
    <arg name="point0_z" value="1.0" />

    <!-- set more waypoints if you need -->
    <arg name="point1_x" value="19.0" />
    <arg name="point1_y" value="10.0" />
    <arg name="point1_z" value="1.0" />

    <arg name="point2_x" value="-19.0" />
    <arg name="point2_y" value="0.0" />
    <arg name="point2_z" value="1.0" />
  </include>

  <!-- trajectory server -->
  <node pkg="plan_manage" name="traj_server" type="traj_server" output="screen">
    <remap from="/position_cmd" to="planning/pos_cmd"/>

    <remap from="/odom_world" to="$(arg odom_topic)"/>
    <param name="traj_server/time_forward" value="1.5" type="double"/>
  </node>

  <node pkg="waypoint_generator" name="waypoint_generator" type="waypoint_generator" output="screen">
    <remap from="~odom" to="$(arg odom_topic)"/>        
    <remap from="~goal" to="/move_base_simple/goal"/>
    <remap from="~traj_start_trigger" to="/traj_start_trigger" />
    <param name="waypoint_type" value="manual-lonely-waypoint"/>    
  </node>

  <!-- use simulator -->
  <include file="$(find plan_manage)/launch/simulator.xml">
    <arg name="map_size_x_" value="$(arg map_size_x)"/>
    <arg name="map_size_y_" value="$(arg map_size_y)"/>
    <arg name="map_size_z_" value="$(arg map_size_z)"/>
    <arg name="c_num" value="100"/>
    <arg name="p_num" value="100"/>

    <arg name="odometry_topic" value="$(arg odom_topic)" />
  </include>

</launch>
