# This is the CMakeCache file.
# For build in directory: /home/<USER>/workspace/src/pose_utils/build
# It was generated by <PERSON>Make: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build shared libraries (DLLs).
BUILD_SHARED_LIBS:BOOL=ON

//Catkin enable testing
CATKIN_ENABLE_TESTING:BOOL=ON

//Prefix to apply to package generated via gendebian
CATKIN_PACKAGE_PREFIX:STRING=

//Catkin skip testing
CATKIN_SKIP_TESTING:BOOL=OFF

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//For backwards compatibility, what version of CMake commands and
// syntax should this version of CMake try to support.
CMAKE_BACKWARDS_COMPATIBILITY:STRING=2.4

//Choose the type of build, options are: None(CMAKE_CXX_FLAGS or
// CMAKE_C_FLAGS used) Debug Release RelWithDebInfo MinSizeRel.
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler.
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//Flags used by the compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the compiler during release minsize builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds (/MD /Ob1 /Oi
// /Ot /Oy /Gs will produce slightly less optimized but smaller
// files).
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the compiler during Release with Debug Info builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler.
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//Flags used by the compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the compiler during debug builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the compiler during release minsize builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the compiler during release builds (/MD /Ob1 /Oi
// /Ot /Oy /Gs will produce slightly less optimized but smaller
// files).
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the compiler during Release with Debug Info builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Flags used by the linker.
CMAKE_EXE_LINKER_FLAGS:STRING=' '

//Flags used by the linker during debug builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=OFF

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules.
CMAKE_MODULE_LINKER_FLAGS:STRING=' '

//Flags used by the linker during debug builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=pose_utils

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Flags used by the linker during the creation of dll's.
CMAKE_SHARED_LINKER_FLAGS:STRING=' '

//Flags used by the linker during debug builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during debug builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during release minsize builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during release builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during Release with Debug Info builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=/opt/ros/indigo/share/ros/core/rosbuild/rostoolchain.cmake

//If true, cmake will use relative paths in makefiles and projects.
CMAKE_USE_RELATIVE_PATHS:BOOL=OFF

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a program.
DOXYGEN_EXECUTABLE:FILEPATH=DOXYGEN_EXECUTABLE-NOTFOUND

//Path to a program.
EMPY_EXECUTABLE:FILEPATH=/usr/bin/empy

//Empy script
EMPY_SCRIPT:STRING=/usr/bin/empy

//Single output directory for building all executables.
EXECUTABLE_OUTPUT_PATH:PATH=

//Path to a file.
GTEST_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GTEST_LIBRARY:FILEPATH=GTEST_LIBRARY-NOTFOUND

//Path to a library.
GTEST_LIBRARY_DEBUG:FILEPATH=GTEST_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
GTEST_MAIN_LIBRARY:FILEPATH=GTEST_MAIN_LIBRARY-NOTFOUND

//Path to a library.
GTEST_MAIN_LIBRARY_DEBUG:FILEPATH=GTEST_MAIN_LIBRARY_DEBUG-NOTFOUND

//Single output directory for building all libraries.
LIBRARY_OUTPUT_PATH:PATH=

//lsb_release executable was found
LSB_FOUND:BOOL=TRUE

//Path to a program.
LSB_RELEASE_EXECUTABLE:FILEPATH=/usr/bin/lsb_release

//Path to a program.
NOSETESTS:FILEPATH=/usr/bin/nosetests-2.7

//Path to a file.
PROJECTCONFIG:FILEPATH=PROJECTCONFIG-NOTFOUND

//Path to a program.
PYTHON_EXECUTABLE:FILEPATH=/usr/bin/python

//Specify specific Python version to use ('major.minor' or 'major')
PYTHON_VERSION:STRING=

//Value Computed by CMake
Project_BINARY_DIR:STATIC=/home/<USER>/workspace/src/pose_utils/build

//Value Computed by CMake
Project_SOURCE_DIR:STATIC=/home/<USER>/workspace/src/pose_utils

//rospack executable
ROSPACK_EXE:FILEPATH=/opt/ros/indigo/bin/rospack

//Path to a library.
RT_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/librt.so

//Enable debian style python package layout
SETUPTOOLS_DEB_LAYOUT:BOOL=ON

//Path to a file.
TOOLCHAINCONFIG:FILEPATH=TOOLCHAINCONFIG-NOTFOUND

//LSB Distrib tag
UBUNTU:BOOL=TRUE

//LSB Distrib - codename tag
UBUNTU_TRUSTY:BOOL=TRUE

//Path to a file.
USERCONFIG:FILEPATH=USERCONFIG-NOTFOUND

//Path to a file.
_CATKIN_GTEST_INCLUDE:FILEPATH=/usr/include/gtest/gtest.h

//Path to a file.
_CATKIN_GTEST_SRC:FILEPATH=/usr/src/gtest/src/gtest.cc

//Path to a file.
_file_name:FILEPATH=/home/<USER>/workspace/src/pose_utils/src/pose_utils.cpp

//The directory containing a CMake configuration file for catkin.
catkin_DIR:PATH=/opt/ros/indigo/share/catkin/cmake

//The directory containing a CMake configuration file for gencpp.
gencpp_DIR:PATH=/opt/ros/indigo/share/gencpp/cmake

//The directory containing a CMake configuration file for genlisp.
genlisp_DIR:PATH=/opt/ros/indigo/share/genlisp/cmake

//The directory containing a CMake configuration file for genmsg.
genmsg_DIR:PATH=/opt/ros/indigo/share/genmsg/cmake

//The directory containing a CMake configuration file for genpy.
genpy_DIR:PATH=/opt/ros/indigo/share/genpy/cmake

//Value Computed by CMake
gtest_BINARY_DIR:STATIC=/home/<USER>/workspace/src/pose_utils/build/gtest

//Dependencies for the target
gtest_LIB_DEPENDS:STATIC=general;-lpthread;

//Value Computed by CMake
gtest_SOURCE_DIR:STATIC=/usr/src/gtest

//Build gtest's sample programs.
gtest_build_samples:BOOL=OFF

//Build all of gtest's own tests.
gtest_build_tests:BOOL=OFF

//Disable uses of pthreads in gtest.
gtest_disable_pthreads:BOOL=OFF

//Use shared (DLL) run-time lib even when Google Test is built
// as static lib.
gtest_force_shared_crt:BOOL=OFF

//Dependencies for the target
gtest_main_LIB_DEPENDS:STATIC=general;-lpthread;general;gtest;

//Path to a library.
lib:FILEPATH=/opt/ros/indigo/lib/libroslib.so

//Value Computed by CMake
pose_utils_BINARY_DIR:STATIC=/home/<USER>/workspace/src/pose_utils/build

//Dependencies for the target
pose_utils_LIB_DEPENDS:STATIC=general;roscpp;general;pthread;general;:/usr/lib/x86_64-linux-gnu/libboost_signals.so;general;:/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;general;rosconsole;general;rosconsole_log4cxx;general;rosconsole_backend_interface;general;:/usr/lib/liblog4cxx.so;general;:/usr/lib/x86_64-linux-gnu/libboost_regex.so;general;xmlrpcpp;general;roscpp_serialization;general;rostime;general;:/usr/lib/x86_64-linux-gnu/libboost_date_time.so;general;cpp_common;general;:/usr/lib/x86_64-linux-gnu/libboost_system.so;general;:/usr/lib/x86_64-linux-gnu/libboost_thread.so;general;:/usr/lib/x86_64-linux-gnu/libpthread.so;general;:/usr/lib/x86_64-linux-gnu/libconsole_bridge.so;general;armadillo;

//Value Computed by CMake
pose_utils_SOURCE_DIR:STATIC=/home/<USER>/workspace/src/pose_utils

//The directory containing a CMake configuration file for roslib.
roslib_DIR:PATH=/opt/ros/indigo/share/roslib/cmake

//The directory containing a CMake configuration file for rosunit.
rosunit_DIR:PATH=/opt/ros/indigo/share/rosunit/cmake


########################
# INTERNAL cache entries
########################

//catkin environment
CATKIN_ENV:INTERNAL=/home/<USER>/workspace/src/pose_utils/build/catkin_generated/env_cached.sh
CATKIN_TEST_RESULTS_DIR:INTERNAL=/home/<USER>/workspace/src/pose_utils/build/test_results
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_BUILD_TOOL
CMAKE_BUILD_TOOL-ADVANCED:INTERNAL=1
//What is the target build tool cmake is generating for.
CMAKE_BUILD_TOOL:INTERNAL=/usr/bin/make
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/workspace/src/pose_utils/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=2
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=8
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=12
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have symbol pthread_create
CMAKE_HAVE_LIBC_CREATE:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Start directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/workspace/src/pose_utils
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_LOCAL_GENERATORS:INTERNAL=2
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-2.8
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//Suppress Warnings that are meant for the author of the CMakeLists.txt
// files.
CMAKE_SUPPRESS_DEVELOPER_WARNINGS:INTERNAL=FALSE
//uname command
CMAKE_UNAME:INTERNAL=/bin/uname
//ADVANCED property for variable: CMAKE_USE_RELATIVE_PATHS
CMAKE_USE_RELATIVE_PATHS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding PythonInterp
FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp:INTERNAL=[/usr/bin/python][v2.7.6()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
GTEST_FROM_SOURCE_FOUND:INTERNAL=TRUE
GTEST_FROM_SOURCE_INCLUDE_DIRS:INTERNAL=/usr/include
GTEST_FROM_SOURCE_LIBRARIES:INTERNAL=gtest
GTEST_FROM_SOURCE_LIBRARY_DIRS:INTERNAL=/home/<USER>/workspace/src/pose_utils/build/gtest
GTEST_FROM_SOURCE_MAIN_LIBRARIES:INTERNAL=gtest_main
//ADVANCED property for variable: GTEST_INCLUDE_DIR
GTEST_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY
GTEST_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_LIBRARY_DEBUG
GTEST_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY
GTEST_MAIN_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GTEST_MAIN_LIBRARY_DEBUG
GTEST_MAIN_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PYTHON_EXECUTABLE
PYTHON_EXECUTABLE-ADVANCED:INTERNAL=1
//This needs to be in PYTHONPATH when 'setup.py install' is called.
//  And it needs to match.  But setuptools won't tell us where
// it will install things.
PYTHON_INSTALL_DIR:INTERNAL=lib/python2.7/dist-packages
_rosbuild_EXPORTS:INTERNAL=
_rosbuild_cached_flag_time:INTERNAL=1411820258.75
_roslang_LANGS:INTERNAL=roslisp
_rospack_deps_manifests_invoke_result:INTERNAL=/opt/ros/indigo/share/cpp_common/package.xml;/opt/ros/indigo/share/rostime/package.xml;/opt/ros/indigo/share/roscpp_traits/package.xml;/opt/ros/indigo/share/roscpp_serialization/package.xml;/opt/ros/indigo/share/genmsg/package.xml;/opt/ros/indigo/share/genpy/package.xml;/opt/ros/indigo/share/message_runtime/package.xml;/opt/ros/indigo/share/catkin/package.xml;/opt/ros/indigo/share/gencpp/package.xml;/opt/ros/indigo/share/genlisp/package.xml;/opt/ros/indigo/share/message_generation/package.xml;/opt/ros/indigo/share/rosbuild/package.xml;/opt/ros/indigo/share/rosconsole/package.xml;/opt/ros/indigo/share/std_msgs/package.xml;/opt/ros/indigo/share/rosgraph_msgs/package.xml;/opt/ros/indigo/share/xmlrpcpp/package.xml;/opt/ros/indigo/share/roscpp/package.xml;/home/<USER>/workspace/src/armadillo/manifest.xml
_rospack_msgsrv_gen_invoke_result:INTERNAL=
pose_utils_CFLAGS_OTHER:INTERNAL=
pose_utils_INCLUDEDIR:INTERNAL=
pose_utils_INCLUDE_DIRS:INTERNAL=/home/<USER>/workspace/src/armadillo/armadillo/include;/opt/ros/indigo/include
pose_utils_LDFLAGS_OTHER:INTERNAL=-Wl,-rpath,/home/<USER>/workspace/src/armadillo/armadillo/lib
pose_utils_LIBRARIES:INTERNAL=roscpp;pthread;:/usr/lib/x86_64-linux-gnu/libboost_signals.so;:/usr/lib/x86_64-linux-gnu/libboost_filesystem.so;rosconsole;rosconsole_log4cxx;rosconsole_backend_interface;:/usr/lib/liblog4cxx.so;:/usr/lib/x86_64-linux-gnu/libboost_regex.so;xmlrpcpp;roscpp_serialization;rostime;:/usr/lib/x86_64-linux-gnu/libboost_date_time.so;cpp_common;:/usr/lib/x86_64-linux-gnu/libboost_system.so;:/usr/lib/x86_64-linux-gnu/libboost_thread.so;:/usr/lib/x86_64-linux-gnu/libpthread.so;:/usr/lib/x86_64-linux-gnu/libconsole_bridge.so;armadillo
pose_utils_LIBRARY_DIRS:INTERNAL=/home/<USER>/workspace/src/armadillo/armadillo/lib;/opt/ros/indigo/lib
pose_utils_cached_manifest_list:INTERNAL=/home/<USER>/workspace/src/pose_utils/manifest.xml;/opt/ros/indigo/share/cpp_common/package.xml;/opt/ros/indigo/share/rostime/package.xml;/opt/ros/indigo/share/roscpp_traits/package.xml;/opt/ros/indigo/share/roscpp_serialization/package.xml;/opt/ros/indigo/share/genmsg/package.xml;/opt/ros/indigo/share/genpy/package.xml;/opt/ros/indigo/share/message_runtime/package.xml;/opt/ros/indigo/share/catkin/package.xml;/opt/ros/indigo/share/gencpp/package.xml;/opt/ros/indigo/share/genlisp/package.xml;/opt/ros/indigo/share/message_generation/package.xml;/opt/ros/indigo/share/rosbuild/package.xml;/opt/ros/indigo/share/rosconsole/package.xml;/opt/ros/indigo/share/std_msgs/package.xml;/opt/ros/indigo/share/rosgraph_msgs/package.xml;/opt/ros/indigo/share/xmlrpcpp/package.xml;/opt/ros/indigo/share/roscpp/package.xml;/home/<USER>/workspace/src/armadillo/manifest.xml
pose_utils_temp:INTERNAL=-Wl,-rpath,/home/<USER>/workspace/src/armadillo/armadillo/lib
roslib_path:INTERNAL=/opt/ros/indigo/share/roslib
roslisp_CMAKE:INTERNAL=/opt/ros/indigo/share/roslisp/rosbuild/roslisp.cmake

