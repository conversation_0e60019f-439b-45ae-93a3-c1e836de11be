#!/usr/bin/env python
PACKAGE='so3_disturbance_generator'
#import roslib; 
#roslib.load_manifest(PACKAGE)

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

# External Disturbance ---------------------------------------------
gen.add("fxy",     double_t, 0, "Force XY"              , 0.0, -1.0, 1.0)
gen.add("stdfxy",  double_t, 0, "Std Force XY"          , 0.0,  0.0, 1.0)
gen.add("fz",      double_t, 0, "Force Z"               , 0.0, -1.0, 1.0)
gen.add("stdfz",   double_t, 0, "Std Force Z"           , 0.0,  0.0, 1.0)
gen.add("mrp",     double_t, 0, "Moment Roll/Pitch"     , 0.0, -0.1, 0.1)
gen.add("stdmrp",  double_t, 0, "Std Moment Roll/Pitch" , 0.0,  0.0, 0.1)
gen.add("myaw",    double_t, 0, "Moment Yaw"            , 0.0, -0.1, 0.1)
gen.add("stdmyaw", double_t, 0, "Std Moment Yaw"        , 0.0,  0.0, 0.1)

# Measurement Noise ------------------------------------------------
gen.add("enable_noisy_odom", bool_t, 0, "Enable Noisy Odometry", False)
gen.add("stdxyz",  double_t, 0, "Std Noise XYZ"        , 0.0, 0.0, 1.0)
gen.add("stdvxyz", double_t, 0, "Std Noise Vel XYZ"    , 0.0, 0.0, 1.0)
gen.add("stdrp",   double_t, 0, "Std Noise Roll/Pitch" , 0.0, 0.0, 0.1)
gen.add("stdyaw",  double_t, 0, "Std Noise Yaw"        , 0.0, 0.0, 0.1)

# Odom Drifting ----------------------------------------------------
gen.add("enable_drift_odom", bool_t, 0, "Enable Drift Odometry", True)
gen.add("stdvdriftxyz", double_t, 0, "Std Noise Vel Drift XYZ", 0.0,  0.0, 0.5)
gen.add("stdvdriftyaw", double_t, 0, "Std Noise Vel Drift Yaw", 0.0,  0.0, 0.5)
gen.add("vdriftx",      double_t, 0, "Vel Drift X"            , 0.0, -0.2, 0.2)
gen.add("vdrifty",      double_t, 0, "Vel Drift Y"            , 0.0, -0.2, 0.2)
gen.add("vdriftz",      double_t, 0, "Vel Drift Z"            , 0.0, -0.2, 0.2)
gen.add("vdriftyaw",    double_t, 0, "Vel Drift Yaw"          , 0.0, -0.1, 0.1)

# Place Holder -----------------------------------------------------
gen.add("place_holder", bool_t, 0, "-------------------------------------------------------------------------------", True)

exit(gen.generate(PACKAGE, "DisturbanceUI", "DisturbanceUI"))

