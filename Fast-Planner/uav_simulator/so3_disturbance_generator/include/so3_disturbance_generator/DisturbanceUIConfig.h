//#line 2 "/opt/ros/indigo/share/dynamic_reconfigure/templates/ConfigType.h.template"
// *********************************************************
// 
// File autogenerated for the so3_disturbance_generator package 
// by the dynamic_reconfigure package.
// Please do not edit.
// 
// ********************************************************/

/***********************************************************
 * Software License Agreement (BSD License)
 *
 *  Copyright (c) 2008, Willow Garage, Inc.
 *  All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *   * Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *   * Redistributions in binary form must reproduce the above
 *     copyright notice, this list of conditions and the following
 *     disclaimer in the documentation and/or other materials provided
 *     with the distribution.
 *   * Neither the name of the Willow Garage nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 *  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *  CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *  LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *  ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 ***********************************************************/

// Author: Blaise Gassend


#ifndef __so3_disturbance_generator__DISTURBANCEUICONFIG_H__
#define __so3_disturbance_generator__DISTURBANCEUICONFIG_H__

#include <dynamic_reconfigure/config_tools.h>
#include <limits>
#include <ros/node_handle.h>
#include <dynamic_reconfigure/ConfigDescription.h>
#include <dynamic_reconfigure/ParamDescription.h>
#include <dynamic_reconfigure/Group.h>
#include <dynamic_reconfigure/config_init_mutex.h>
#include <boost/any.hpp>

namespace so3_disturbance_generator
{
  class DisturbanceUIConfigStatics;
  
  class DisturbanceUIConfig
  {
  public:
    class AbstractParamDescription : public dynamic_reconfigure::ParamDescription
    {
    public:
      AbstractParamDescription(std::string n, std::string t, uint32_t l, 
          std::string d, std::string e)
      {
        name = n;
        type = t;
        level = l;
        description = d;
        edit_method = e;
      }
      
      virtual void clamp(DisturbanceUIConfig &config, const DisturbanceUIConfig &max, const DisturbanceUIConfig &min) const = 0;
      virtual void calcLevel(uint32_t &level, const DisturbanceUIConfig &config1, const DisturbanceUIConfig &config2) const = 0;
      virtual void fromServer(const ros::NodeHandle &nh, DisturbanceUIConfig &config) const = 0;
      virtual void toServer(const ros::NodeHandle &nh, const DisturbanceUIConfig &config) const = 0;
      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, DisturbanceUIConfig &config) const = 0;
      virtual void toMessage(dynamic_reconfigure::Config &msg, const DisturbanceUIConfig &config) const = 0;
      virtual void getValue(const DisturbanceUIConfig &config, boost::any &val) const = 0;
    };

    typedef boost::shared_ptr<AbstractParamDescription> AbstractParamDescriptionPtr;
    typedef boost::shared_ptr<const AbstractParamDescription> AbstractParamDescriptionConstPtr;
    
    template <class T>
    class ParamDescription : public AbstractParamDescription
    {
    public:
      ParamDescription(std::string name, std::string type, uint32_t level, 
          std::string description, std::string edit_method, T DisturbanceUIConfig::* f) :
        AbstractParamDescription(name, type, level, description, edit_method),
        field(f)
      {}

      T (DisturbanceUIConfig::* field);

      virtual void clamp(DisturbanceUIConfig &config, const DisturbanceUIConfig &max, const DisturbanceUIConfig &min) const
      {
        if (config.*field > max.*field)
          config.*field = max.*field;
        
        if (config.*field < min.*field)
          config.*field = min.*field;
      }

      virtual void calcLevel(uint32_t &comb_level, const DisturbanceUIConfig &config1, const DisturbanceUIConfig &config2) const
      {
        if (config1.*field != config2.*field)
          comb_level |= level;
      }

      virtual void fromServer(const ros::NodeHandle &nh, DisturbanceUIConfig &config) const
      {
        nh.getParam(name, config.*field);
      }

      virtual void toServer(const ros::NodeHandle &nh, const DisturbanceUIConfig &config) const
      {
        nh.setParam(name, config.*field);
      }

      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, DisturbanceUIConfig &config) const
      {
        return dynamic_reconfigure::ConfigTools::getParameter(msg, name, config.*field);
      }

      virtual void toMessage(dynamic_reconfigure::Config &msg, const DisturbanceUIConfig &config) const
      {
        dynamic_reconfigure::ConfigTools::appendParameter(msg, name, config.*field);
      }

      virtual void getValue(const DisturbanceUIConfig &config, boost::any &val) const
      {
        val = config.*field;
      }
    };

    class AbstractGroupDescription : public dynamic_reconfigure::Group
    {
      public:
      AbstractGroupDescription(std::string n, std::string t, int p, int i, bool s)
      {
        name = n;
        type = t;
        parent = p;
        state = s;
        id = i;
      }

      std::vector<AbstractParamDescriptionConstPtr> abstract_parameters;
      bool state;

      virtual void toMessage(dynamic_reconfigure::Config &msg, const boost::any &config) const = 0;
      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, boost::any &config) const =0;
      virtual void updateParams(boost::any &cfg, DisturbanceUIConfig &top) const= 0;
      virtual void setInitialState(boost::any &cfg) const = 0;


      void convertParams()
      {
        for(std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = abstract_parameters.begin(); i != abstract_parameters.end(); ++i)
        {
          parameters.push_back(dynamic_reconfigure::ParamDescription(**i));
        }
      }
    };

    typedef boost::shared_ptr<AbstractGroupDescription> AbstractGroupDescriptionPtr;
    typedef boost::shared_ptr<const AbstractGroupDescription> AbstractGroupDescriptionConstPtr;

    template<class T, class PT>
    class GroupDescription : public AbstractGroupDescription
    {
    public:
      GroupDescription(std::string name, std::string type, int parent, int id, bool s, T PT::* f) : AbstractGroupDescription(name, type, parent, id, s), field(f)
      {
      }

      GroupDescription(const GroupDescription<T, PT>& g): AbstractGroupDescription(g.name, g.type, g.parent, g.id, g.state), field(g.field), groups(g.groups)
      {
        parameters = g.parameters;
        abstract_parameters = g.abstract_parameters;
      }

      virtual bool fromMessage(const dynamic_reconfigure::Config &msg, boost::any &cfg) const
      {
        PT* config = boost::any_cast<PT*>(cfg);
        if(!dynamic_reconfigure::ConfigTools::getGroupState(msg, name, (*config).*field))
          return false;

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = &((*config).*field);
          if(!(*i)->fromMessage(msg, n))
            return false;
        }

        return true;
      }

      virtual void setInitialState(boost::any &cfg) const
      {
        PT* config = boost::any_cast<PT*>(cfg);
        T* group = &((*config).*field);
        group->state = state;

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = boost::any(&((*config).*field));
          (*i)->setInitialState(n);
        }

      }

      virtual void updateParams(boost::any &cfg, DisturbanceUIConfig &top) const
      {
        PT* config = boost::any_cast<PT*>(cfg);

        T* f = &((*config).*field);
        f->setParams(top, abstract_parameters);

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          boost::any n = &((*config).*field);
          (*i)->updateParams(n, top);
        }
      }

      virtual void toMessage(dynamic_reconfigure::Config &msg, const boost::any &cfg) const
      {
        const PT config = boost::any_cast<PT>(cfg);
        dynamic_reconfigure::ConfigTools::appendGroup<T>(msg, name, id, parent, config.*field);

        for(std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = groups.begin(); i != groups.end(); ++i)
        {
          (*i)->toMessage(msg, config.*field);
        }
      }

      T (PT::* field);
      std::vector<DisturbanceUIConfig::AbstractGroupDescriptionConstPtr> groups;
    };
    
class DEFAULT
{
  public:
    DEFAULT()
    {
      state = true;
      name = "Default";
    }

    void setParams(DisturbanceUIConfig &config, const std::vector<AbstractParamDescriptionConstPtr> params)
    {
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator _i = params.begin(); _i != params.end(); ++_i)
      {
        boost::any val;
        (*_i)->getValue(config, val);

        if("fxy"==(*_i)->name){fxy = boost::any_cast<double>(val);}
        if("stdfxy"==(*_i)->name){stdfxy = boost::any_cast<double>(val);}
        if("fz"==(*_i)->name){fz = boost::any_cast<double>(val);}
        if("stdfz"==(*_i)->name){stdfz = boost::any_cast<double>(val);}
        if("mrp"==(*_i)->name){mrp = boost::any_cast<double>(val);}
        if("stdmrp"==(*_i)->name){stdmrp = boost::any_cast<double>(val);}
        if("myaw"==(*_i)->name){myaw = boost::any_cast<double>(val);}
        if("stdmyaw"==(*_i)->name){stdmyaw = boost::any_cast<double>(val);}
        if("enable_noisy_odom"==(*_i)->name){enable_noisy_odom = boost::any_cast<bool>(val);}
        if("stdxyz"==(*_i)->name){stdxyz = boost::any_cast<double>(val);}
        if("stdvxyz"==(*_i)->name){stdvxyz = boost::any_cast<double>(val);}
        if("stdrp"==(*_i)->name){stdrp = boost::any_cast<double>(val);}
        if("stdyaw"==(*_i)->name){stdyaw = boost::any_cast<double>(val);}
        if("enable_drift_odom"==(*_i)->name){enable_drift_odom = boost::any_cast<bool>(val);}
        if("stdvdriftxyz"==(*_i)->name){stdvdriftxyz = boost::any_cast<double>(val);}
        if("stdvdriftyaw"==(*_i)->name){stdvdriftyaw = boost::any_cast<double>(val);}
        if("vdriftx"==(*_i)->name){vdriftx = boost::any_cast<double>(val);}
        if("vdrifty"==(*_i)->name){vdrifty = boost::any_cast<double>(val);}
        if("vdriftz"==(*_i)->name){vdriftz = boost::any_cast<double>(val);}
        if("vdriftyaw"==(*_i)->name){vdriftyaw = boost::any_cast<double>(val);}
        if("place_holder"==(*_i)->name){place_holder = boost::any_cast<bool>(val);}
      }
    }

    double fxy;
double stdfxy;
double fz;
double stdfz;
double mrp;
double stdmrp;
double myaw;
double stdmyaw;
bool enable_noisy_odom;
double stdxyz;
double stdvxyz;
double stdrp;
double stdyaw;
bool enable_drift_odom;
double stdvdriftxyz;
double stdvdriftyaw;
double vdriftx;
double vdrifty;
double vdriftz;
double vdriftyaw;
bool place_holder;

    bool state;
    std::string name;

    
}groups;



//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double fxy;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdfxy;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double fz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdfz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double mrp;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdmrp;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double myaw;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdmyaw;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      bool enable_noisy_odom;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdxyz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdvxyz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdrp;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdyaw;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      bool enable_drift_odom;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdvdriftxyz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double stdvdriftyaw;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double vdriftx;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double vdrifty;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double vdriftz;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      double vdriftyaw;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      bool place_holder;
//#line 255 "/opt/ros/indigo/share/dynamic_reconfigure/templates/ConfigType.h.template"

    bool __fromMessage__(dynamic_reconfigure::Config &msg)
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();

      int count = 0;
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        if ((*i)->fromMessage(msg, *this))
          count++;

      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); i ++)
      {
        if ((*i)->id == 0)
        {
          boost::any n = boost::any(this);
          (*i)->updateParams(n, *this);
          (*i)->fromMessage(msg, n);
        }
      }

      if (count != dynamic_reconfigure::ConfigTools::size(msg))
      {
        ROS_ERROR("DisturbanceUIConfig::__fromMessage__ called with an unexpected parameter.");
        ROS_ERROR("Booleans:");
        for (unsigned int i = 0; i < msg.bools.size(); i++)
          ROS_ERROR("  %s", msg.bools[i].name.c_str());
        ROS_ERROR("Integers:");
        for (unsigned int i = 0; i < msg.ints.size(); i++)
          ROS_ERROR("  %s", msg.ints[i].name.c_str());
        ROS_ERROR("Doubles:");
        for (unsigned int i = 0; i < msg.doubles.size(); i++)
          ROS_ERROR("  %s", msg.doubles[i].name.c_str());
        ROS_ERROR("Strings:");
        for (unsigned int i = 0; i < msg.strs.size(); i++)
          ROS_ERROR("  %s", msg.strs[i].name.c_str());
        // @todo Check that there are no duplicates. Make this error more
        // explicit.
        return false;
      }
      return true;
    }

    // This version of __toMessage__ is used during initialization of
    // statics when __getParamDescriptions__ can't be called yet.
    void __toMessage__(dynamic_reconfigure::Config &msg, const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__, const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__) const
    {
      dynamic_reconfigure::ConfigTools::clear(msg);
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->toMessage(msg, *this);

      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); ++i)
      {
        if((*i)->id == 0)
        {
          (*i)->toMessage(msg, *this);
        }
      }
    }
    
    void __toMessage__(dynamic_reconfigure::Config &msg) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();
      __toMessage__(msg, __param_descriptions__, __group_descriptions__);
    }
    
    void __toServer__(const ros::NodeHandle &nh) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->toServer(nh, *this);
    }

    void __fromServer__(const ros::NodeHandle &nh)
    {
      static bool setup=false;

      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->fromServer(nh, *this);

      const std::vector<AbstractGroupDescriptionConstPtr> &__group_descriptions__ = __getGroupDescriptions__();
      for (std::vector<AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); i++){
        if (!setup && (*i)->id == 0) {
          setup = true;
          boost::any n = boost::any(this);
          (*i)->setInitialState(n);
        }
      }
    }

    void __clamp__()
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      const DisturbanceUIConfig &__max__ = __getMax__();
      const DisturbanceUIConfig &__min__ = __getMin__();
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->clamp(*this, __max__, __min__);
    }

    uint32_t __level__(const DisturbanceUIConfig &config) const
    {
      const std::vector<AbstractParamDescriptionConstPtr> &__param_descriptions__ = __getParamDescriptions__();
      uint32_t level = 0;
      for (std::vector<AbstractParamDescriptionConstPtr>::const_iterator i = __param_descriptions__.begin(); i != __param_descriptions__.end(); ++i)
        (*i)->calcLevel(level, config, *this);
      return level;
    }
    
    static const dynamic_reconfigure::ConfigDescription &__getDescriptionMessage__();
    static const DisturbanceUIConfig &__getDefault__();
    static const DisturbanceUIConfig &__getMax__();
    static const DisturbanceUIConfig &__getMin__();
    static const std::vector<AbstractParamDescriptionConstPtr> &__getParamDescriptions__();
    static const std::vector<AbstractGroupDescriptionConstPtr> &__getGroupDescriptions__();
    
  private:
    static const DisturbanceUIConfigStatics *__get_statics__();
  };
  
  template <> // Max and min are ignored for strings.
  inline void DisturbanceUIConfig::ParamDescription<std::string>::clamp(DisturbanceUIConfig &config, const DisturbanceUIConfig &max, const DisturbanceUIConfig &min) const
  {
    return;
  }

  class DisturbanceUIConfigStatics
  {
    friend class DisturbanceUIConfig;
    
    DisturbanceUIConfigStatics()
    {
DisturbanceUIConfig::GroupDescription<DisturbanceUIConfig::DEFAULT, DisturbanceUIConfig> Default("Default", "", 0, 0, true, &DisturbanceUIConfig::groups);
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.fxy = -1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.fxy = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.fxy = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("fxy", "double", 0, "Force XY", "", &DisturbanceUIConfig::fxy)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("fxy", "double", 0, "Force XY", "", &DisturbanceUIConfig::fxy)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdfxy = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdfxy = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdfxy = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdfxy", "double", 0, "Std Force XY", "", &DisturbanceUIConfig::stdfxy)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdfxy", "double", 0, "Std Force XY", "", &DisturbanceUIConfig::stdfxy)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.fz = -1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.fz = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.fz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("fz", "double", 0, "Force Z", "", &DisturbanceUIConfig::fz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("fz", "double", 0, "Force Z", "", &DisturbanceUIConfig::fz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdfz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdfz = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdfz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdfz", "double", 0, "Std Force Z", "", &DisturbanceUIConfig::stdfz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdfz", "double", 0, "Std Force Z", "", &DisturbanceUIConfig::stdfz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.mrp = -0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.mrp = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.mrp = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("mrp", "double", 0, "Moment Roll/Pitch", "", &DisturbanceUIConfig::mrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("mrp", "double", 0, "Moment Roll/Pitch", "", &DisturbanceUIConfig::mrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdmrp = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdmrp = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdmrp = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdmrp", "double", 0, "Std Moment Roll/Pitch", "", &DisturbanceUIConfig::stdmrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdmrp", "double", 0, "Std Moment Roll/Pitch", "", &DisturbanceUIConfig::stdmrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.myaw = -0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.myaw = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.myaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("myaw", "double", 0, "Moment Yaw", "", &DisturbanceUIConfig::myaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("myaw", "double", 0, "Moment Yaw", "", &DisturbanceUIConfig::myaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdmyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdmyaw = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdmyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdmyaw", "double", 0, "Std Moment Yaw", "", &DisturbanceUIConfig::stdmyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdmyaw", "double", 0, "Std Moment Yaw", "", &DisturbanceUIConfig::stdmyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.enable_noisy_odom = 0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.enable_noisy_odom = 1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.enable_noisy_odom = 0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("enable_noisy_odom", "bool", 0, "Enable Noisy Odometry", "", &DisturbanceUIConfig::enable_noisy_odom)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("enable_noisy_odom", "bool", 0, "Enable Noisy Odometry", "", &DisturbanceUIConfig::enable_noisy_odom)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdxyz = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdxyz", "double", 0, "Std Noise XYZ", "", &DisturbanceUIConfig::stdxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdxyz", "double", 0, "Std Noise XYZ", "", &DisturbanceUIConfig::stdxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdvxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdvxyz = 1.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdvxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvxyz", "double", 0, "Std Noise Vel XYZ", "", &DisturbanceUIConfig::stdvxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvxyz", "double", 0, "Std Noise Vel XYZ", "", &DisturbanceUIConfig::stdvxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdrp = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdrp = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdrp = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdrp", "double", 0, "Std Noise Roll/Pitch", "", &DisturbanceUIConfig::stdrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdrp", "double", 0, "Std Noise Roll/Pitch", "", &DisturbanceUIConfig::stdrp)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdyaw = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdyaw", "double", 0, "Std Noise Yaw", "", &DisturbanceUIConfig::stdyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdyaw", "double", 0, "Std Noise Yaw", "", &DisturbanceUIConfig::stdyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.enable_drift_odom = 0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.enable_drift_odom = 1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.enable_drift_odom = 1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("enable_drift_odom", "bool", 0, "Enable Drift Odometry", "", &DisturbanceUIConfig::enable_drift_odom)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("enable_drift_odom", "bool", 0, "Enable Drift Odometry", "", &DisturbanceUIConfig::enable_drift_odom)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdvdriftxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdvdriftxyz = 0.5;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdvdriftxyz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvdriftxyz", "double", 0, "Std Noise Vel Drift XYZ", "", &DisturbanceUIConfig::stdvdriftxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvdriftxyz", "double", 0, "Std Noise Vel Drift XYZ", "", &DisturbanceUIConfig::stdvdriftxyz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.stdvdriftyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.stdvdriftyaw = 0.5;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.stdvdriftyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvdriftyaw", "double", 0, "Std Noise Vel Drift Yaw", "", &DisturbanceUIConfig::stdvdriftyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("stdvdriftyaw", "double", 0, "Std Noise Vel Drift Yaw", "", &DisturbanceUIConfig::stdvdriftyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.vdriftx = -0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.vdriftx = 0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.vdriftx = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftx", "double", 0, "Vel Drift X", "", &DisturbanceUIConfig::vdriftx)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftx", "double", 0, "Vel Drift X", "", &DisturbanceUIConfig::vdriftx)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.vdrifty = -0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.vdrifty = 0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.vdrifty = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdrifty", "double", 0, "Vel Drift Y", "", &DisturbanceUIConfig::vdrifty)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdrifty", "double", 0, "Vel Drift Y", "", &DisturbanceUIConfig::vdrifty)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.vdriftz = -0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.vdriftz = 0.2;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.vdriftz = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftz", "double", 0, "Vel Drift Z", "", &DisturbanceUIConfig::vdriftz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftz", "double", 0, "Vel Drift Z", "", &DisturbanceUIConfig::vdriftz)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.vdriftyaw = -0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.vdriftyaw = 0.1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.vdriftyaw = 0.0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftyaw", "double", 0, "Vel Drift Yaw", "", &DisturbanceUIConfig::vdriftyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<double>("vdriftyaw", "double", 0, "Vel Drift Yaw", "", &DisturbanceUIConfig::vdriftyaw)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __min__.place_holder = 0;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __max__.place_holder = 1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __default__.place_holder = 1;
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.abstract_parameters.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("place_holder", "bool", 0, "-------------------------------------------------------------------------------", "", &DisturbanceUIConfig::place_holder)));
//#line 259 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __param_descriptions__.push_back(DisturbanceUIConfig::AbstractParamDescriptionConstPtr(new DisturbanceUIConfig::ParamDescription<bool>("place_holder", "bool", 0, "-------------------------------------------------------------------------------", "", &DisturbanceUIConfig::place_holder)));
//#line 233 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      Default.convertParams();
//#line 233 "/opt/ros/indigo/lib/python2.7/dist-packages/dynamic_reconfigure/parameter_generator.py"
      __group_descriptions__.push_back(DisturbanceUIConfig::AbstractGroupDescriptionConstPtr(new DisturbanceUIConfig::GroupDescription<DisturbanceUIConfig::DEFAULT, DisturbanceUIConfig>(Default)));
//#line 390 "/opt/ros/indigo/share/dynamic_reconfigure/templates/ConfigType.h.template"

      for (std::vector<DisturbanceUIConfig::AbstractGroupDescriptionConstPtr>::const_iterator i = __group_descriptions__.begin(); i != __group_descriptions__.end(); ++i)
      {
        __description_message__.groups.push_back(**i);
      }
      __max__.__toMessage__(__description_message__.max, __param_descriptions__, __group_descriptions__); 
      __min__.__toMessage__(__description_message__.min, __param_descriptions__, __group_descriptions__); 
      __default__.__toMessage__(__description_message__.dflt, __param_descriptions__, __group_descriptions__); 
    }
    std::vector<DisturbanceUIConfig::AbstractParamDescriptionConstPtr> __param_descriptions__;
    std::vector<DisturbanceUIConfig::AbstractGroupDescriptionConstPtr> __group_descriptions__;
    DisturbanceUIConfig __max__;
    DisturbanceUIConfig __min__;
    DisturbanceUIConfig __default__;
    dynamic_reconfigure::ConfigDescription __description_message__;

    static const DisturbanceUIConfigStatics *get_instance()
    {
      // Split this off in a separate function because I know that
      // instance will get initialized the first time get_instance is
      // called, and I am guaranteeing that get_instance gets called at
      // most once.
      static DisturbanceUIConfigStatics instance;
      return &instance;
    }
  };

  inline const dynamic_reconfigure::ConfigDescription &DisturbanceUIConfig::__getDescriptionMessage__() 
  {
    return __get_statics__()->__description_message__;
  }

  inline const DisturbanceUIConfig &DisturbanceUIConfig::__getDefault__()
  {
    return __get_statics__()->__default__;
  }
  
  inline const DisturbanceUIConfig &DisturbanceUIConfig::__getMax__()
  {
    return __get_statics__()->__max__;
  }
  
  inline const DisturbanceUIConfig &DisturbanceUIConfig::__getMin__()
  {
    return __get_statics__()->__min__;
  }
  
  inline const std::vector<DisturbanceUIConfig::AbstractParamDescriptionConstPtr> &DisturbanceUIConfig::__getParamDescriptions__()
  {
    return __get_statics__()->__param_descriptions__;
  }

  inline const std::vector<DisturbanceUIConfig::AbstractGroupDescriptionConstPtr> &DisturbanceUIConfig::__getGroupDescriptions__()
  {
    return __get_statics__()->__group_descriptions__;
  }

  inline const DisturbanceUIConfigStatics *DisturbanceUIConfig::__get_statics__()
  {
    const static DisturbanceUIConfigStatics *statics;
  
    if (statics) // Common case
      return statics;

    boost::mutex::scoped_lock lock(dynamic_reconfigure::__init_mutex__);

    if (statics) // In case we lost a race.
      return statics;

    statics = DisturbanceUIConfigStatics::get_instance();
    
    return statics;
  }


}

#endif // __DISTURBANCEUIRECONFIGURATOR_H__
