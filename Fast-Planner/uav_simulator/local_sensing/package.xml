<?xml version="1.0"?>
<package>
  <name>local_sensing_node</name>
  <version>0.1.0</version>
  <description>
    render depth from depth
  </description>
  <author><PERSON><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>
  <license>GPLv3</license>

  <!-- Dependencies which this package needs to build itself. -->
  <buildtool_depend>catkin</buildtool_depend>

  <!-- Dependencies needed to compile this package. -->
  <build_depend>cmake_modules</build_depend> <!-- for FindEigen.cmake -->
  <build_depend>roscpp</build_depend>
  <build_depend>roslib</build_depend>
  <build_depend>svo_msgs</build_depend>
  <build_depend>cv_bridge</build_depend>
  <build_depend>image_transport</build_depend>
  <build_depend>vikit_ros</build_depend>
  <build_depend>pcl_ros</build_depend>
  <build_depend>dynamic_reconfigure</build_depend>
  <build_depend>quadrotor_msgs</build_depend>

  <!-- Dependencies needed after this package is compiled. -->
  <run_depend>roscpp</run_depend>
  <run_depend>roslib</run_depend>
  <run_depend>svo_msgs</run_depend>
  <run_depend>cv_bridge</run_depend>
  <run_depend>image_transport</run_depend>
  <run_depend>vikit_ros</run_depend>
  <run_depend>pcl_ros</run_depend>
  <run_depend>dynamic_reconfigure</run_depend>
  <run_depend>quadrotor_msgs</run_depend>

</package>
