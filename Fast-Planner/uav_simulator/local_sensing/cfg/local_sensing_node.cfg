#!/usr/bin/env python
PACKAGE = "local_sensing_node"

from dynamic_reconfigure.parameter_generator_catkin import *

gen = ParameterGenerator()

gen.add("tx", double_t, 0, "tx", 0.0, -1000.0, 1000.0)
gen.add("ty", double_t, 0, "ty", 0.0, -1000.0, 1000.0)
gen.add("tz", double_t, 0, "tz", 0.0, -1000.0, 1000.0)
gen.add("axis_x", double_t, 0, "axis_x", 0.0, -360.0, 360.0)
gen.add("axis_y", double_t, 0, "axis_y", 0.0, -360.0, 360.0)
gen.add("axis_z", double_t, 0, "axis_z", 0.0, -360.0, 360.0)

exit(gen.generate(PACKAGE, "local_sensing_node", "local_sensing_node"))