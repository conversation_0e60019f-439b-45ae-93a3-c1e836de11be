[/============================================================================
  Boost.odeint

  Copyright (c) 2009-2012 <PERSON><PERSON>t
  Copyright (c) 2009-2012 <PERSON>

  Use, modification and distribution is subject to the Boost Software License,
  Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
  http://www.boost.org/LICENSE_1_0.txt)
=============================================================================/]



[section Dense Output Stepper]

This concept specifies the interface a dense output stepper has to fulfill to be used  within __integrate_functions.

[heading Description]
A dense output stepper following this Dense Output Stepper concept provides the possibility to perform a single step of the solution /x(t)/ of an ODE to obtain /x(t+dt)/.
The step-size `dt` might be adjusted automatically due to error control.
Dense output steppers also can interpolate the solution to calculate the state /x(t')/ at any point /t <= t' <= t+dt/.

[heading Associated types]

*  '''<para>'''[*state_type]'''</para>'''
'''<para>'''`Stepper::state_type`'''</para>'''
'''<para>'''The type characterizing the state of the ODE, hence ['x].'''</para>'''

*  '''<para>'''[*deriv_type]'''</para>'''
'''<para>'''`Stepper::deriv_type`'''</para>'''
'''<para>'''The type characterizing the derivative of the ODE, hence ['d x/dt].'''</para>'''

*  '''<para>'''[*time_type]'''</para>'''
'''<para>'''`Stepper::time_type`'''</para>'''
'''<para>'''The type characterizing the dependent variable of the ODE, hence the time ['t].'''</para>'''

*  '''<para>'''[*value_type]'''</para>'''
'''<para>'''`Stepper::value_type`'''</para>'''
'''<para>'''The numerical data type which is used within the stepper, something like `float`, `double`, `complex&lt; double &gt;`.'''</para>'''

*  '''<para>'''[*stepper_category]'''</para>'''
'''<para>'''`Stepper::stepper_category`'''</para>'''
'''<para>'''A tag type characterizing the category of the stepper. This type must be convertible to `dense_output_stepper_tag`.'''</para>'''


[heading Notation]

[variablelist 
  [[`Stepper`] [A type that is a model of Dense Output Stepper]]
  [[`State`] [A type representing the state /x/ of the ODE]]
  [[`stepper`] [An object of type `Stepper`]]
  [[`x0`, `x`] [Object of type `State`]]
  [[`t0`, `dt0`, `t`] [Objects of type `Stepper::time_type`]]
  [[`sys`] [An object defining the ODE, should be a model of __system, __symplectic_system, __simple_symplectic_system or __implicit_system.]]
]

[heading Valid Expressions]

[table
  [[Name] [Expression] [Type] [Semantics]]

  [[Initialize integration] [`stepper.initialize( x0 , t0 , dt0 )`] [void] [Initializes the stepper with initial values `x0`, `t0` and `dt0`.]]

  [[Do step] [`stepper.do_step( sys )`] [`std::pair< Stepper::time_type , Stepper::time_type >`] [Performs one step using the ODE defined by `sys`. The step-size might be changed internally due to error control. This function returns a pair containing `t` and `t+dt` representing the interval for which interpolation can be performed.] ]

  [/ [Do step with reference] [`stepper.do_step( boost::ref( sys ) )`] [`std::pair< Stepper::time_type , Stepper::time_type >`] [Same as above with `System` as reference] ]

  [[Do interpolation] [`stepper.calc_state( t_inter , x )`] [`void`] [Performs the interpolation to calculate /x(t[sub inter]/) where /t <= t[sub inter] <= t+dt/.]]

  [[Get current time] [`stepper.current_time()`] [`const Stepper::time_type&`] [Returns the current time /t+dt/ of the stepper, that is the end time of the last step and the starting time for the next call of `do_step`]]

  [[Get current state] [`stepper.current_state()`] [`const Stepper::state_type&`] [Returns the current state of the stepper, that is /x(t+dt)/, the state at the time returned by `stepper.current_time()`]] 
]

[heading Models]

* `dense_output_controlled_explicit_fsal< controlled_error_stepper_fsal< runge_kutta_dopri5 >`
* `bulirsch_stoer_dense_out`
* `rosenbrock4_dense_output`

[endsect]