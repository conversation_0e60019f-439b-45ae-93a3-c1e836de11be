#!/bin/bash
# Btraj环境快速测试脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "Btraj环境快速测试"

# 1. 检查必要文件
info "检查项目文件..."
required_dirs=("Btraj" "plan_utils" "mosek")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        success "✓ $dir 目录存在"
    else
        error "✗ $dir 目录不存在"
        exit 1
    fi
done

# 检查Mosek许可证
if [ -f "mosek/mosek.lic" ]; then
    success "✓ Mosek许可证存在"
else
    warn "⚠ Mosek许可证不存在，优化功能可能受限"
fi

# 2. 检查Docker环境
info "检查Docker环境..."
if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
    success "✓ Docker Compose 可用"
else
    error "✗ Docker Compose 不可用"
    exit 1
fi

# 3. 检查X11环境
info "检查X11环境..."
XAUTH=/tmp/.docker.xauth
if [ -n "$DISPLAY" ]; then
    success "✓ DISPLAY 已设置: $DISPLAY"
else
    warn "⚠ DISPLAY 未设置"
fi

if [ -S "/tmp/.X11-unix/X0" ]; then
    success "✓ X11套接字存在"
else
    warn "⚠ X11套接字不存在"
fi

# 4. 准备X认证
info "准备X11认证..."
touch "$XAUTH"
chmod 666 "$XAUTH"
if [ -f "$HOME/.Xauthority" ] && [ -n "$DISPLAY" ]; then
    xauth nlist "$DISPLAY" | sed -e 's/^..../ffff/' | xauth -f "$XAUTH" nmerge - 2>/dev/null || true
    success "✓ X认证文件已准备"
else
    warn "⚠ 无法准备完整的X认证"
fi

# 5. 创建数据目录
info "创建数据持久化目录..."
mkdir -p docker_data/{catkin_build,catkin_devel}
success "✓ 数据目录已创建"

# 6. 验证docker-compose配置
info "验证Docker Compose配置..."
if docker compose config >/dev/null 2>&1; then
    success "✓ Docker Compose配置有效"
else
    error "✗ Docker Compose配置无效"
    exit 1
fi

echo ""
success "=== 环境检查完成 ==="
echo ""
info "Btraj开发流程:"
echo "1. 启动环境: ./start-ros-gui.sh (选择选项1)"
echo "2. 进入容器: ./start-ros-gui.sh (选择选项6)"
echo "3. 测试环境: ./test-btraj-env.sh"
echo "4. 构建项目: ./build-btraj.sh"
echo "5. 启动仿真: ./start-btraj.sh"

echo ""
info "故障排除:"
echo "- GUI问题: 确保NoMachine连接正常"
echo "- 构建问题: 检查依赖和Mosek许可证"
echo "- 权限问题: 检查文件挂载权限"

echo ""
success "准备就绪！可以开始Btraj开发了。"
