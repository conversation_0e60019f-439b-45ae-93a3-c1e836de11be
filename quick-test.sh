#!/bin/bash
# 快速测试新的Docker Compose配置

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "快速测试基于成功配置的Docker Compose环境"

# 1. 清理旧环境
info "清理旧环境..."
docker compose down 2>/dev/null || true
docker system prune -f >/dev/null 2>&1 || true

# 2. 准备X认证
info "准备X11认证..."
XAUTH=/tmp/.docker.xauth
touch "$XAUTH"
chmod 666 "$XAUTH"

if [ -f "$HOME/.Xauthority" ] && [ -n "$DISPLAY" ]; then
    xauth nlist "$DISPLAY" | sed -e 's/^..../ffff/' | xauth -f "$XAUTH" nmerge - 2>/dev/null || true
    success "X认证文件已准备"
else
    warn "无法准备X认证，但继续测试"
fi

# 3. 构建并启动容器
info "构建并启动容器..."
if docker compose up --build -d; then
    success "容器启动成功"
else
    error "容器启动失败"
    exit 1
fi

# 4. 等待容器就绪
info "等待容器就绪..."
sleep 5

# 5. 检查容器状态
info "检查容器状态..."
if docker compose ps | grep -q "Up"; then
    success "容器运行正常"
else
    error "容器未正常运行"
    docker compose logs
    exit 1
fi

# 6. 测试基础命令
info "测试基础ROS命令..."
if docker compose exec -T ros-noetic bash -c "source ~/.bashrc && which roscore" >/dev/null 2>&1; then
    success "ROS命令可用"
else
    error "ROS命令不可用"
fi

# 7. 测试GUI环境
info "测试GUI环境..."
docker compose exec -T ros-noetic bash -c "
    source ~/.bashrc
    echo 'DISPLAY: \$DISPLAY'
    echo 'XAUTHORITY: \$XAUTHORITY'
    ls -la /tmp/.X11-unix/ 2>/dev/null || echo 'X11套接字不存在'
    ls -la /tmp/.docker.xauth 2>/dev/null || echo 'X认证文件不存在'
"

echo ""
success "=== 测试完成 ==="
echo ""
info "下一步操作:"
echo "1. 进入容器: docker compose exec ros-noetic bash"
echo "2. 运行GUI测试: ./test-gui.sh"
echo "3. 测试RViz: roscore & 然后 rviz"
echo ""
info "如果要停止容器: docker compose down"
