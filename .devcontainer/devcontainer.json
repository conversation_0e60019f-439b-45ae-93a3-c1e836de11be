{
    // 开发容器名称，显示在VS Code中
    "name": "Btraj ROS Workspace",
    // 构建配置：使用父目录的Dockerfile
    "build": {
        "dockerfile": "Dockerfile"
    },
    // 容器内的工作目录，VS Code将在此目录打开
    "workspaceFolder": "/workspace/catkin_ws/",
    // 挂载配置：挂载Btraj项目到ROS工作空间中
    "mounts": [
        "source=${localWorkspaceFolder}/Btraj,target=/workspace/catkin_ws/src/Btraj,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/plan_utils,target=/workspace/catkin_ws/src/plan_utils,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/DecompROS,target=/workspace/catkin_ws/src/DecompROS,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/mosek,target=/root/mosek,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/.devcontainer/start-btraj-sim.sh,target=/usr/local/bin/start-btraj-sim.sh,type=bind,consistency=cached",
        "source=${localWorkspaceFolder}/.devcontainer/build-workspace.sh,target=/usr/local/bin/build-workspace.sh,type=bind,consistency=cached"
    ],
    // Dev Container功能：预配置的开发环境组件
    "features": {
        // 通用工具功能：创建用户、设置权限等
        "ghcr.io/devcontainers/features/common-utils:2": {
            "username": "btraj", // 创建用户名为btraj
            "userUid": 1000, // 用户ID，与宿主机匹配避免权限问题
            "userGid": 1000, // 组ID，与宿主机匹配
            "installZsh": false, // 不安装zsh，保持bash
            "upgradePackages": false // 禁用系统包升级，保持环境稳定
        }
    },
    // 容器启动后执行的命令
    "postCreateCommand": "echo 'Container created successfully! GUI support configured for RViz.' && echo 'Run build-workspace.sh to build the catkin workspace.'",
    // VS Code自定义设置
    "customizations": {
        "vscode": {
            // 自动安装的VS Code扩展
            "extensions": [
                "ms-vscode.cpptools-extension-pack", // C++开发工具包
                "ms-vscode.cmake-tools", // CMake工具
                "ms-python.python" // Python支持（ROS脚本）
            ]
        }
    },
    // Docker运行参数，传递给docker run命令
    "runArgs": [
        "--privileged", // 特权模式，ROS仿真需要
        "-v",
        "/tmp/.X11-unix:/tmp/.X11-unix:rw", // 挂载X11套接字，支持GUI
        "-v",
        "${env:HOME}/.Xauthority:/home/<USER>/.Xauthority:rw", // 挂载X11认证文件到用户目录
        "-v",
        "/dev/dri:/dev/dri", // 挂载GPU设备，硬件加速（可选）
        "-e",
        "DISPLAY=${env:DISPLAY:-:0}", // 传递DISPLAY环境变量，默认:0
        "-e",
        "QT_X11_NO_MITSHM=1", // 解决Qt应用X11共享内存问题
        "-e",
        "XAUTHORITY=/home/<USER>/.Xauthority", // 设置X11认证文件路径
        "-e",
        "LIBGL_ALWAYS_INDIRECT=1", // SSH/NoMachine环境使用间接渲染
        "-e",
        "NVIDIA_VISIBLE_DEVICES=all", // NVIDIA GPU支持（可选）
        "-e",
        "NVIDIA_DRIVER_CAPABILITIES=all", // NVIDIA驱动功能（可选）
        "--network=host", // 使用宿主机网络，ROS节点通信
        "--ipc=host", // 共享进程间通信，提高GUI性能
        "--security-opt",
        "seccomp=unconfined" // 解决某些系统调用限制问题
    ]
}