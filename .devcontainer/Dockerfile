# 基础镜像：使用官方ROS Noetic完整桌面版
# 包含ROS核心、可视化工具(RViz)、仿真工具(Gazebo)等
FROM osrf/ros:noetic-desktop-full

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive
# 安装开发工具和现代化命令行工具
# RUN: 在镜像构建时执行命令
# apt-get update: 更新软件包列表
# apt-get install -y: 自动确认安装以下包
RUN apt-get update && apt-get install -y \
    bash-completion \
    tree \
    htop \
    vim \
    git \
    curl \
    # Btraj项目依赖
    libarmadillo-dev \
    # ROS包依赖
    ros-noetic-cv-bridge \
    ros-noetic-image-transport \
    ros-noetic-visualization-msgs \
    ros-noetic-sensor-msgs \
    ros-noetic-std-msgs \
    ros-noetic-message-generation \
    ros-noetic-message-runtime \
    # OpenCV和图像处理
    libopencv-dev \
    # Eigen3数学库
    libeigen3-dev \
    # PCL点云库
    libpcl-dev \
    ros-noetic-pcl-conversions \
    ros-noetic-pcl-ros \
    # GUI相关依赖
    xauth x11-apps mesa-utils \
    libxcb1 libxcb-render0 libxcb-shape0 libxcb-xfixes0 \
    libxrandr2 libxinerama1 libxcursor1 libxi6 libgl1-mesa-glx \
    # Qt5相关依赖（RViz需要）
    qt5-default libqt5opengl5-dev \
    # OpenGL相关依赖
    libgl1-mesa-dev libglu1-mesa-dev freeglut3-dev \
    # 清理apt缓存，减小最终镜像大小
    && rm -rf /var/lib/apt/lists/*

# 安装zoxide (智能目录跳转工具)
RUN curl -sS https://raw.githubusercontent.com/ajeetdsouza/zoxide/main/install.sh | bash

# 安装fzf (模糊搜索工具) - 使用最新版本
RUN git clone --depth 1 https://github.com/junegunn/fzf.git /opt/fzf && \
    /opt/fzf/install --all --no-update-rc

# 配置bash环境 - 使用分组减少RUN层数
RUN { \
    echo ''; \
    echo '# 启用颜色支持和别名'; \
    echo 'export TERM=xterm-256color'; \
    echo 'alias ll="ls -alF --color=auto"'; \
    echo 'alias la="ls -A --color=auto"'; \
    echo 'alias l="ls -CF --color=auto"'; \
    echo 'alias ls="ls --color=auto"'; \
    echo 'alias grep="grep --color=auto"'; \
    echo ''; \
    echo '# 启用bash补全'; \
    echo 'if [ -f /usr/share/bash-completion/bash_completion ]; then'; \
    echo '    . /usr/share/bash-completion/bash_completion'; \
    echo 'fi'; \
    echo ''; \
    echo '# 设置更好的命令提示符'; \
    echo 'PS1="\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ "'; \
    echo ''; \
    echo '# ROS环境自动加载'; \
    echo 'source /opt/ros/noetic/setup.bash'; \
    echo ''; \
    echo '# 自动检测ROS工作空间环境'; \
    echo 'if [ -f /workspace/catkin_ws/devel/setup.bash ]; then'; \
    echo '    source /workspace/catkin_ws/devel/setup.bash'; \
    echo 'elif [ -f devel/setup.bash ]; then'; \
    echo '    source devel/setup.bash'; \
    echo 'elif [ -f ../devel/setup.bash ]; then'; \
    echo '    source ../devel/setup.bash'; \
    echo 'fi'; \
    echo ''; \
    echo '# Mosek环境变量'; \
    echo 'export MOSEKLM_LICENSE_FILE=/root/mosek/mosek.lic'; \
    echo 'export PATH="/root/mosek/9.3/tools/platform/linux64x86/bin:$PATH"'; \
    echo ''; \
    echo '# X11环境变量设置'; \
    echo 'export QT_X11_NO_MITSHM=1'; \
    echo 'export LIBGL_ALWAYS_INDIRECT=1'; \
    echo 'export DISPLAY=${DISPLAY:-:0}'; \
    echo '# NoMachine/SSH环境优化'; \
    echo 'export QT_GRAPHICSSYSTEM=native'; \
    echo 'export XDG_RUNTIME_DIR=/tmp/runtime-btraj'; \
    echo 'mkdir -p /tmp/runtime-btraj && chmod 700 /tmp/runtime-btraj'; \
    echo ''; \
    echo '# zoxide智能目录跳转'; \
    echo 'eval "$(zoxide init bash)"'; \
    echo ''; \
    echo '# fzf模糊搜索配置'; \
    echo 'export PATH="/opt/fzf/bin:$PATH"'; \
    echo 'source /opt/fzf/shell/completion.bash'; \
    echo 'source /opt/fzf/shell/key-bindings.bash'; \
    } >> /etc/bash.bashrc

# 创建默认工作目录，devcontainer会重新挂载
WORKDIR /workspace

# 创建X11启动脚本
RUN echo '#!/bin/bash' > /usr/local/bin/setup-x11.sh && \
    echo 'set -e' >> /usr/local/bin/setup-x11.sh && \
    echo '' >> /usr/local/bin/setup-x11.sh && \
    echo '# 检查和设置用户目录权限' >> /usr/local/bin/setup-x11.sh && \
    echo 'if [ -f /home/<USER>/.Xauthority ]; then' >> /usr/local/bin/setup-x11.sh && \
    echo '    chown btraj:btraj /home/<USER>/.Xauthority' >> /usr/local/bin/setup-x11.sh && \
    echo '    chmod 600 /home/<USER>/.Xauthority' >> /usr/local/bin/setup-x11.sh && \
    echo '    echo "X11认证文件权限已设置"' >> /usr/local/bin/setup-x11.sh && \
    echo 'fi' >> /usr/local/bin/setup-x11.sh && \
    echo '' >> /usr/local/bin/setup-x11.sh && \
    echo '# 检查X11环境' >> /usr/local/bin/setup-x11.sh && \
    echo 'if [ -z "$DISPLAY" ]; then' >> /usr/local/bin/setup-x11.sh && \
    echo '    export DISPLAY=:0' >> /usr/local/bin/setup-x11.sh && \
    echo '    echo "DISPLAY设置为: $DISPLAY"' >> /usr/local/bin/setup-x11.sh && \
    echo 'else' >> /usr/local/bin/setup-x11.sh && \
    echo '    echo "DISPLAY 已设置为: $DISPLAY"' >> /usr/local/bin/setup-x11.sh && \
    echo 'fi' >> /usr/local/bin/setup-x11.sh && \
    echo '' >> /usr/local/bin/setup-x11.sh && \
    echo '# 测试GPU支持' >> /usr/local/bin/setup-x11.sh && \
    echo 'if [ -e /dev/dri ]; then' >> /usr/local/bin/setup-x11.sh && \
    echo '    echo "GPU设备可用: $(ls /dev/dri/)"' >> /usr/local/bin/setup-x11.sh && \
    echo 'else' >> /usr/local/bin/setup-x11.sh && \
    echo '    echo "GPU设备不可用，使用软件渲染"' >> /usr/local/bin/setup-x11.sh && \
    echo 'fi' >> /usr/local/bin/setup-x11.sh && \
    echo '' >> /usr/local/bin/setup-x11.sh && \
    echo '# 测试GUI支持' >> /usr/local/bin/setup-x11.sh && \
    echo 'echo "测试X11连接..."' >> /usr/local/bin/setup-x11.sh && \
    echo 'if command -v xeyes >/dev/null 2>&1; then' >> /usr/local/bin/setup-x11.sh && \
    echo '    timeout 3 xeyes &>/dev/null && echo "X11 GUI支持正常!" || echo "X11 GUI测试失败"' >> /usr/local/bin/setup-x11.sh && \
    echo 'fi' >> /usr/local/bin/setup-x11.sh && \
    echo '' >> /usr/local/bin/setup-x11.sh && \
    echo 'echo "X11设置完成!"' >> /usr/local/bin/setup-x11.sh && \
    chmod +x /usr/local/bin/setup-x11.sh

# # 声明ROS常用端口（虽然使用host网络，但作为文档说明）
# # ROS Master默认端口
# EXPOSE 11311
# # 其他ROS节点可能使用的端口范围
# EXPOSE 11312-11400

# 设置默认环境变量
ENV DISPLAY=:0
ENV QT_X11_NO_MITSHM=1
ENV LIBGL_ALWAYS_INDIRECT=1
ENV XDG_RUNTIME_DIR=/tmp/runtime-btraj

# Mosek环境变量
ENV MOSEKLM_LICENSE_FILE=/root/mosek/mosek.lic
ENV PATH="/root/mosek/9.3/tools/platform/linux64x86/bin:$PATH"

# 默认命令：启动bash交互式shell并初始化X11
CMD ["/bin/bash", "-c", "mkdir -p $XDG_RUNTIME_DIR && chmod 700 $XDG_RUNTIME_DIR && setup-x11.sh && exec bash"]