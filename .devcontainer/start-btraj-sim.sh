#!/bin/bash
# Btraj仿真启动脚本 - 适配Windows SSH -> Linux -> Docker -> NoMachine环境

set -e

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "Btraj仿真环境初始化..."

# 1. 检查ROS环境
info "检查ROS环境..."
if ! command -v roscore >/dev/null 2>&1; then
    error "ROS命令不可用，检查ROS安装"
    exit 1
fi

# 确保ROS环境已source
if [ -z "$ROS_DISTRO" ]; then
    info "加载ROS环境..."
    source /opt/ros/noetic/setup.bash
fi

# 始终source工作空间环境
if [ -f /workspace/catkin_ws/devel/setup.bash ]; then
    info "加载工作空间环境..."
    source /workspace/catkin_ws/devel/setup.bash
else
    warn "工作空间环境文件不存在，可能需要先构建"
fi

success "ROS环境: $ROS_DISTRO"

# 2. 检查工作空间
info "检查Btraj工作空间..."
if [ ! -d "/workspace/catkin_ws/src/Btraj" ]; then
    error "Btraj项目未找到在 /workspace/catkin_ws/src/Btraj"
    exit 1
fi

# 3. 检查是否已构建
if [ ! -f "/workspace/catkin_ws/devel/setup.bash" ]; then
    warn "工作空间未构建，正在构建..."
    cd /workspace/catkin_ws
    catkin_make
    if [ $? -ne 0 ]; then
        error "构建失败，请检查依赖"
        exit 1
    fi
    source devel/setup.bash
fi

# 4. 检查和设置X11环境（适配NoMachine）
info "检查X11/GUI环境..."

# 设置X11环境变量
export DISPLAY=${DISPLAY:-:0}
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=1
export XDG_RUNTIME_DIR=/tmp/runtime-btraj
mkdir -p $XDG_RUNTIME_DIR && chmod 700 $XDG_RUNTIME_DIR

info "X11环境变量设置:"
echo "  DISPLAY=$DISPLAY"
echo "  QT_X11_NO_MITSHM=$QT_X11_NO_MITSHM"
echo "  LIBGL_ALWAYS_INDIRECT=$LIBGL_ALWAYS_INDIRECT"

# 测试X11连接
if command -v xset >/dev/null 2>&1; then
    if timeout 5 xset q >/dev/null 2>&1; then
        success "X11连接正常"
    else
        warn "X11连接测试失败，但将尝试启动GUI"
        warn "请确保NoMachine连接正常"
    fi
else
    warn "xset命令不可用，跳过X11测试"
fi

# 检查X11套接字
if [ -S "/tmp/.X11-unix/X0" ]; then
    success "X11套接字存在"
else
    warn "X11套接字不存在，GUI可能无法显示"
fi

# 5. 检查依赖包
info "检查关键依赖..."
missing_deps=()

# 检查Mosek许可证
if [ ! -f "/root/mosek/mosek.lic" ] && [ ! -f "$HOME/mosek/mosek.lic" ]; then
    missing_deps+=("Mosek许可证文件")
fi

# 检查plan_utils
if [ ! -d "/workspace/catkin_ws/src/plan_utils" ]; then
    missing_deps+=("plan_utils包")
fi

# 检查关键ROS包 (仅检查Btraj必需的)
ros_packages=("visualization_msgs" "nav_msgs" "tf")
for pkg in "${ros_packages[@]}"; do
    if ! rospack find "$pkg" >/dev/null 2>&1; then
        missing_deps+=("ROS包: $pkg")
    fi
done

if [ ${#missing_deps[@]} -gt 0 ]; then
    error "缺少以下依赖:"
    for dep in "${missing_deps[@]}"; do
        echo "  - $dep"
    done
    exit 1
fi

# 6. 启动仿真选项菜单
info "Btraj仿真启动选项:"
echo "1) 启动完整仿真 (roscore + rviz + btraj)"
echo "2) 仅启动ROS Master"
echo "3) 仅启动RViz" 
echo "4) 启动Btraj规划节点"
echo "5) 构建工作空间"
echo "6) 环境诊断"
echo -n "请选择 (1-6): "
read choice

case $choice in
    1)
        info "启动完整Btraj仿真..."

        # 确保环境变量正确设置
        export DISPLAY=${DISPLAY:-:0}
        export QT_X11_NO_MITSHM=1
        export LIBGL_ALWAYS_INDIRECT=1

        # 在后台启动roscore
        info "启动ROS Master..."
        roscore &
        sleep 3

        # 启动仿真（在NoMachine桌面显示）
        info "启动Btraj仿真 (RViz将在NoMachine桌面显示)..."
        info "如果RViz无法显示，请检查NoMachine连接"

        # 使用exec来替换当前进程，这样Ctrl+C能正确工作
        exec roslaunch bezier_planer simulation.launch
        ;;
    2)
        info "启动ROS Master..."
        roscore
        ;;
    3)
        info "启动RViz..."
        export DISPLAY=${DISPLAY:-:0}
        export QT_X11_NO_MITSHM=1
        export LIBGL_ALWAYS_INDIRECT=1
        rviz
        ;;
    4)
        info "启动Btraj规划节点..."
        export DISPLAY=${DISPLAY:-:0}
        export QT_X11_NO_MITSHM=1
        export LIBGL_ALWAYS_INDIRECT=1
        roslaunch bezier_planer simulation.launch
        ;;
    5)
        info "构建工作空间..."
        cd /workspace/catkin_ws
        catkin_make
        success "构建完成"
        ;;
    6)
        info "运行环境诊断..."
        echo "=== ROS环境 ==="
        echo "ROS_DISTRO: $ROS_DISTRO"
        echo "ROS_MASTER_URI: $ROS_MASTER_URI"
        echo ""
        echo "=== X11环境 ==="
        echo "DISPLAY: $DISPLAY"
        echo "XAUTHORITY: $XAUTHORITY"
        echo ""
        echo "=== 工作空间 ==="
        ls -la /workspace/catkin_ws/src/
        echo ""
        echo "=== GPU信息 ==="
        if [ -e /dev/dri ]; then
            ls -la /dev/dri/
        else
            echo "GPU设备不可用"
        fi
        ;;
    *)
        error "无效选择"
        exit 1
        ;;
esac