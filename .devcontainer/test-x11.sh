#!/bin/bash
# X11显示测试脚本 - 专门用于诊断GUI显示问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "X11显示环境测试..."

echo ""
info "=== 当前X11环境变量 ==="
echo "DISPLAY: ${DISPLAY:-未设置}"
echo "XAUTHORITY: ${XAUTHORITY:-未设置}"
echo "QT_X11_NO_MITSHM: ${QT_X11_NO_MITSHM:-未设置}"
echo "LIBGL_ALWAYS_INDIRECT: ${LIBGL_ALWAYS_INDIRECT:-未设置}"
echo "XDG_RUNTIME_DIR: ${XDG_RUNTIME_DIR:-未设置}"

echo ""
info "=== 设置推荐的X11环境变量 ==="
export DISPLAY=${DISPLAY:-:0}
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=1
export XDG_RUNTIME_DIR=/tmp/runtime-btraj
mkdir -p $XDG_RUNTIME_DIR && chmod 700 $XDG_RUNTIME_DIR

success "环境变量已设置:"
echo "  DISPLAY=$DISPLAY"
echo "  QT_X11_NO_MITSHM=$QT_X11_NO_MITSHM"
echo "  LIBGL_ALWAYS_INDIRECT=$LIBGL_ALWAYS_INDIRECT"
echo "  XDG_RUNTIME_DIR=$XDG_RUNTIME_DIR"

echo ""
info "=== X11文件和套接字检查 ==="

# 检查X11套接字
if [ -S "/tmp/.X11-unix/X0" ]; then
    success "X11套接字存在: /tmp/.X11-unix/X0"
else
    error "X11套接字不存在: /tmp/.X11-unix/X0"
    echo "可用的X11套接字:"
    ls -la /tmp/.X11-unix/ 2>/dev/null || echo "  无X11套接字目录"
fi

# 检查X认证文件
if [ -n "$XAUTHORITY" ] && [ -f "$XAUTHORITY" ]; then
    success "X认证文件存在: $XAUTHORITY"
    ls -la "$XAUTHORITY"
else
    warn "X认证文件不存在或未设置"
    echo "常见位置检查:"
    for auth_file in "/home/<USER>/.Xauthority" "/root/.Xauthority" "$HOME/.Xauthority"; do
        if [ -f "$auth_file" ]; then
            echo "  找到: $auth_file"
            ls -la "$auth_file"
        else
            echo "  不存在: $auth_file"
        fi
    done
fi

echo ""
info "=== X11连接测试 ==="

# 基础X11测试
if command -v xset >/dev/null 2>&1; then
    info "测试xset..."
    if timeout 10 xset q >/dev/null 2>&1; then
        success "xset测试通过"
    else
        error "xset测试失败"
    fi
else
    warn "xset命令不可用"
fi

# 测试简单的X11应用
if command -v xeyes >/dev/null 2>&1; then
    info "测试xeyes (3秒后自动关闭)..."
    if timeout 3 xeyes >/dev/null 2>&1; then
        success "xeyes测试通过"
    else
        error "xeyes测试失败"
    fi
else
    warn "xeyes命令不可用"
fi

echo ""
info "=== Qt/RViz特定测试 ==="

# 测试Qt环境
if command -v qtdiag >/dev/null 2>&1; then
    info "运行Qt诊断..."
    qtdiag 2>&1 | head -20
else
    warn "qtdiag不可用，跳过Qt诊断"
fi

echo ""
info "=== 建议的解决方案 ==="

if [ ! -S "/tmp/.X11-unix/X0" ]; then
    error "主要问题: X11套接字不存在"
    echo ""
    echo "解决方案:"
    echo "1. 确保NoMachine正在运行并已连接"
    echo "2. 在Linux主机上检查DISPLAY环境变量:"
    echo "   echo \$DISPLAY"
    echo "3. 确保Docker容器正确挂载了X11套接字:"
    echo "   -v /tmp/.X11-unix:/tmp/.X11-unix:rw"
    echo ""
elif ! timeout 5 xset q >/dev/null 2>&1; then
    warn "X11套接字存在但连接失败"
    echo ""
    echo "可能的解决方案:"
    echo "1. 检查X认证权限:"
    echo "   ls -la \$XAUTHORITY"
    echo "2. 尝试不同的DISPLAY值:"
    echo "   export DISPLAY=:1  # 或其他值"
    echo "3. 重启NoMachine连接"
    echo ""
else
    success "X11基础连接正常"
    echo ""
    echo "如果RViz仍然无法启动，尝试:"
    echo "1. 使用软件渲染:"
    echo "   export LIBGL_ALWAYS_SOFTWARE=1"
    echo "2. 禁用GPU加速:"
    echo "   export QT_QUICK_BACKEND=software"
    echo "3. 检查RViz配置文件是否存在"
fi

echo ""
info "=== 快速RViz测试 ==="
echo "是否要尝试启动RViz进行测试? (y/N)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    info "启动RViz测试 (10秒后自动关闭)..."
    timeout 10 rviz >/dev/null 2>&1 && success "RViz启动成功!" || error "RViz启动失败"
fi

echo ""
info "X11测试完成"
