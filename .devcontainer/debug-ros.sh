#!/bin/bash
# ROS环境诊断脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "ROS环境诊断开始..."

echo ""
info "=== ROS基础环境 ==="
echo "ROS_DISTRO: ${ROS_DISTRO:-未设置}"
echo "ROS_MASTER_URI: ${ROS_MASTER_URI:-未设置}"
echo "ROS_PACKAGE_PATH: ${ROS_PACKAGE_PATH:-未设置}"
echo "PYTHONPATH: ${PYTHONPATH:-未设置}"

echo ""
info "=== 工作空间状态 ==="
echo "当前目录: $(pwd)"
echo "工作空间结构:"
ls -la /workspace/catkin_ws/ || echo "工作空间不存在"

if [ -d "/workspace/catkin_ws/src" ]; then
    echo ""
    echo "src目录内容:"
    ls -la /workspace/catkin_ws/src/
else
    error "src目录不存在"
fi

if [ -d "/workspace/catkin_ws/devel" ]; then
    echo ""
    echo "devel目录内容:"
    ls -la /workspace/catkin_ws/devel/
    
    if [ -f "/workspace/catkin_ws/devel/setup.bash" ]; then
        success "setup.bash存在"
        echo "setup.bash内容预览:"
        head -10 /workspace/catkin_ws/devel/setup.bash
    else
        error "setup.bash不存在"
    fi
else
    error "devel目录不存在 - 工作空间未构建"
fi

echo ""
info "=== 包检测 ==="

# 检查Btraj包
if [ -d "/workspace/catkin_ws/src/Btraj" ]; then
    success "Btraj源码存在"
    echo "Btraj包内容:"
    ls -la /workspace/catkin_ws/src/Btraj/
    
    if [ -f "/workspace/catkin_ws/src/Btraj/package.xml" ]; then
        echo "包名: $(grep '<name>' /workspace/catkin_ws/src/Btraj/package.xml | sed 's/.*<name>\(.*\)<\/name>.*/\1/')"
    fi
else
    error "Btraj源码不存在"
fi

# 检查plan_utils
if [ -d "/workspace/catkin_ws/src/plan_utils" ]; then
    success "plan_utils存在"
    echo "plan_utils子包:"
    ls -la /workspace/catkin_ws/src/plan_utils/
else
    error "plan_utils不存在"
fi

echo ""
info "=== ROS包路径测试 ==="

# 重新source环境
info "重新加载ROS环境..."
source /opt/ros/noetic/setup.bash

if [ -f "/workspace/catkin_ws/devel/setup.bash" ]; then
    info "加载工作空间环境..."
    source /workspace/catkin_ws/devel/setup.bash
    success "工作空间环境已加载"
else
    warn "工作空间环境文件不存在，跳过"
fi

echo "更新后的ROS_PACKAGE_PATH:"
echo "$ROS_PACKAGE_PATH"

echo ""
info "=== 包查找测试 ==="

# 测试rospack
if command -v rospack >/dev/null 2>&1; then
    echo "测试rospack find bezier_planer:"
    if rospack find bezier_planer 2>/dev/null; then
        success "bezier_planer包找到"
    else
        error "bezier_planer包未找到"
        echo "可用的包列表 (前20个):"
        rospack list | head -20
    fi
    
    echo ""
    echo "测试其他相关包:"
    for pkg in "waypoint_generator" "quadrotor_msgs" "odom_visualization"; do
        if rospack find "$pkg" 2>/dev/null; then
            success "$pkg 找到"
        else
            warn "$pkg 未找到"
        fi
    done
else
    error "rospack命令不可用"
fi

echo ""
info "=== 构建日志检查 ==="
if [ -f "/workspace/catkin_ws/build/CMakeCache.txt" ]; then
    echo "构建配置存在，检查构建日志..."
    if [ -f "/workspace/catkin_ws/build/bezier_planer/CMakeFiles/CMakeError.log" ]; then
        echo "发现bezier_planer构建错误:"
        tail -10 "/workspace/catkin_ws/build/bezier_planer/CMakeFiles/CMakeError.log"
    fi
else
    warn "没有构建缓存，可能需要重新构建"
fi

echo ""
info "=== 建议解决方案 ==="
if ! rospack find bezier_planer >/dev/null 2>&1; then
    echo "1. 重新构建工作空间:"
    echo "   cd /workspace/catkin_ws && catkin_make clean && catkin_make"
    echo ""
    echo "2. 重新加载环境:"
    echo "   source /workspace/catkin_ws/devel/setup.bash"
    echo ""
    echo "3. 检查构建错误:"
    echo "   查看上面的构建日志输出"
fi

echo ""
info "诊断完成"
