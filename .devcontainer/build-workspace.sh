#!/bin/bash
# Btraj工作空间构建脚本
# 自动处理依赖安装和工作空间构建

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m' 
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }

info "开始构建Btraj工作空间..."

# 1. 检查并安装缺失的ROS依赖
info "检查ROS依赖..."
cd /workspace/catkin_ws

# 使用rosdep安装依赖
if command -v rosdep >/dev/null 2>&1; then
    info "更新rosdep数据库..."
    rosdep update || warn "rosdep更新失败，继续构建..."
    
    info "安装工作空间依赖..."
    rosdep install --from-paths src --ignore-src -r -y || warn "部分依赖安装失败，继续构建..."
else
    warn "rosdep不可用，跳过自动依赖安装"
fi

# 2. 检查关键文件
info "检查关键组件..."
required_packages=("Btraj" "plan_utils")
for pkg in "${required_packages[@]}"; do
    if [ ! -d "src/$pkg" ]; then
        error "缺少必需包: $pkg"
        exit 1
    fi
done

# 3. 构建工作空间
info "构建catkin工作空间..."
catkin_make -j$(nproc)

if [ $? -eq 0 ]; then
    success "工作空间构建成功!"
    
    # 4. 设置环境
    info "设置环境变量..."
    source devel/setup.bash
    
    # 5. 验证构建结果
    info "验证构建结果..."
    if rospack find bezier_planer >/dev/null 2>&1; then
        success "Btraj包构建成功!"
    else
        warn "Btraj包可能构建失败"
    fi
    
    echo ""
    success "构建完成! 可以运行以下命令启动仿真:"
    echo "  start-btraj-sim.sh"
    echo "  或者手动运行:"
    echo "  roslaunch bezier_planer simulation.launch"
    
else
    error "工作空间构建失败!"
    exit 1
fi
