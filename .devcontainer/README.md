# Btraj DevContainer 配置说明

## 🚀 快速开始 (专注Btraj)

### 1. 环境要求
- Windows主机 + VSCode
- Linux主机 (通过SSH连接)
- Docker已安装在Linux主机上
- NoMachine用于GUI显示

### 2. 四步启动Btraj

1. **启动DevContainer**
   - 在VSCode中按 `Ctrl+Shift+P`
   - 选择 "Dev Containers: Reopen in Container"
   - 等待容器构建完成 (首次约5-10分钟)

2. **测试环境** (可选但推荐)
   ```bash
   test-btraj-env.sh
   ```

3. **构建工作空间**
   ```bash
   build-workspace.sh
   ```

4. **启动仿真**
   ```bash
   # 推荐: 使用简化启动脚本
   start-btraj-simple.sh

   # 或使用完整启动脚本
   start-btraj-sim.sh
   ```

### 🎯 预期结果
- ROS Master启动
- RViz在NoMachine桌面显示
- 可以在RViz中设置目标点进行路径规划

## 📁 核心项目结构

```
learnPNCProj_2025-08/
├── .devcontainer/             # Docker配置
├── Btraj/                     # 主要项目代码
├── plan_utils/                # 规划工具包 (包含quadrotor_msgs等)
└── mosek/                     # Mosek许可证 (优化求解器)
```

## 🔧 配置特性

### Docker镜像特性
- **基础镜像**: `osrf/ros:noetic-desktop-full`
- **GUI支持**: 完整的X11转发配置
- **开发工具**: 现代化命令行工具(zoxide, fzf)
- **ROS依赖**: 预装所需的ROS包

### 挂载配置
- Btraj项目 → `/workspace/catkin_ws/src/Btraj`
- plan_utils → `/workspace/catkin_ws/src/plan_utils`
- Mosek许可证 → `/root/mosek`

### 环境变量
- `DISPLAY=:0` - X11显示
- `MOSEKLM_LICENSE_FILE=/root/mosek/mosek.lic` - Mosek许可证
- `QT_X11_NO_MITSHM=1` - Qt X11兼容性

## 🛠️ 故障排除

### ❌ X11/GUI显示问题 (最常见)
**症状**: `qt.qpa.xcb: could not connect to display`

**解决方案**:
1. **使用简化启动脚本**: `start-btraj-simple.sh`
2. **测试X11环境**: `test-x11.sh`
3. **确保NoMachine连接**: 在Linux主机上确保NoMachine正在运行
4. **检查DISPLAY**: 在Linux主机终端运行 `echo $DISPLAY`
5. **尝试无GUI模式**: 选择启动选项2 (仅ROS节点)

### ❌ ROS包找不到问题
**症状**: `RLException: [simulation.launch] is neither a launch file`

**解决方案**:
```bash
# 确保加载工作空间环境
source /workspace/catkin_ws/devel/setup.bash
# 或重新构建
build-workspace.sh
```

### 构建失败
1. 检查依赖: `rosdep check --from-paths src --ignore-src`
2. 清理构建: `catkin_make clean`
3. 重新构建: `build-workspace.sh`

### Mosek许可证问题
1. 确保许可证文件存在: `ls -la /root/mosek/mosek.lic`
2. 检查环境变量: `echo $MOSEKLM_LICENSE_FILE`

## 📝 使用技巧

### 常用命令
```bash
# 环境诊断
start-btraj-sim.sh  # 选择选项6

# 仅启动ROS Master
roscore

# 仅启动RViz
rviz

# 查看ROS话题
rostopic list

# 查看节点状态
rosnode list
```

### VSCode扩展
自动安装的扩展:
- C++ Extension Pack
- CMake Tools  
- Python Extension

### 开发工作流
1. 修改代码
2. 重新构建: `catkin_make`
3. 重启节点或重新启动仿真
4. 在RViz中查看结果

## 🔄 更新配置

如需修改配置:
1. 编辑 `.devcontainer/` 下的文件
2. 重新构建容器: "Dev Containers: Rebuild Container"
3. 或者修改后重启容器: "Dev Containers: Reopen in Container"
